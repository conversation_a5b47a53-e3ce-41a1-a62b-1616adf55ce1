"""
Town of West Haven Event Handlers

This module contains level-specific event handlers and quest logic for West Haven.
"""

import time
from typing import Optional, Dict, Any
from src.game_core.events import GameEvent
from src.game_core.quest_manager import QuestState
from src.game_core import Position
from src.application.use_cases import CreateMonsterUseCase
from src.infrastructure.logging import get_logger


class TownWestHavenEventHandlers:
    """Event handlers for West Haven level-specific logic."""

    def __init__(self, event_bus, quest_manager):
        """
        Initialize West Haven event handlers.

        Args:
            event_bus: Event bus for game events
            quest_manager: Quest manager for quest state
        """
        self.event_bus = event_bus
        self.quest_manager = quest_manager
        self.logger = get_logger(__name__)

        # Track NPC dialog states for sequential dialog
        self.npc_dialog_states: Dict[str, Dict[str, Any]] = {}

        # Register quest definitions
        if self.quest_manager:
            from .quest_definitions import get_west_haven_quests
            for quest_def in get_west_haven_quests():
                self.quest_manager.register_quest(quest_def)

    def register_handlers(self) -> None:
        """Register all event handlers for this level."""
        self.event_bus.subscribe("entity_defeated", self.on_monster_defeated)
        self.event_bus.subscribe("npc_interaction", self.on_npc_interaction)
        self.event_bus.subscribe("quest_started", self.on_quest_started)
        self.event_bus.subscribe("quest_completed", self.on_quest_completed)
        self.event_bus.subscribe("quest_closed", self.on_quest_closed)
        self.event_bus.subscribe("objective_completed", self.on_objective_completed)

    def unregister_handlers(self) -> None:
        """Unregister all event handlers when leaving the level."""
        self.event_bus.unsubscribe("entity_defeated", self.on_monster_defeated)
        self.event_bus.unsubscribe("npc_interaction", self.on_npc_interaction)
        self.event_bus.unsubscribe("quest_started", self.on_quest_started)
        self.event_bus.unsubscribe("quest_completed", self.on_quest_completed)
        self.event_bus.unsubscribe("quest_closed", self.on_quest_closed)
        self.event_bus.unsubscribe("objective_completed", self.on_objective_completed)
    
    def on_level_loaded(self, event: GameEvent) -> None:
        """Handle level loaded event."""
        self.logger.info("West Haven level loaded")
    
    def on_monster_defeated(self, event: GameEvent) -> None:
        """Handle monster defeated events for quest completion."""
        entity_id = event.data.get("entity_id", "")

        # Check if this is the quest goblin
        if "quest_goblin_goblin_threat_west_haven" in entity_id:
            self._complete_goblin_quest()
    
    def on_npc_interaction(self, event: GameEvent) -> None:
        """Handle player interactions with NPCs."""
        npc_id = event.data.get("npc_id", "")
        npc_position = event.data.get("npc_position")
        
        if not self.quest_manager:
            return
        
        # Check if this is the mayor - the quest giver
        if npc_id.startswith("mayor"):
            self._handle_mayor_interaction(npc_id, npc_position)
    
    def _handle_mayor_interaction(self, npc_id: str, position: tuple) -> None:
        """Handle interaction with the Mayor."""
        quest_id = "goblin_threat_west_haven"
        quest_state = self.quest_manager.get_quest_state(quest_id)
        
        if quest_state == QuestState.NOT_STARTED:
            # Start the quest
            success = self.quest_manager.start_quest(quest_id)
            if success:
                self.logger.info("Mayor: Quest started - Goblin Threat")
                # Update NPC dialog state
                self.npc_dialog_states[npc_id] = {
                    "quest_offered": True,
                    "quest_accepted": True
                }
                
                # Spawn the quest goblin
                self._spawn_quest_goblin(quest_id)
        
        elif quest_state == QuestState.ACTIVE:
            self.logger.info("Mayor: The goblin is still out there. Please be careful!")
        
        elif quest_state == QuestState.COMPLETED:
            # Close the quest and give rewards
            success = self.quest_manager.close_quest(quest_id)
            if success:
                self.logger.info("Mayor: Thank you for dealing with that goblin! Here's your reward.")
                # Emit reward event
                self.event_bus.publish(GameEvent(
                    event_type="quest_reward",
                    timestamp=time.time(),
                    data={
                        "quest_id": quest_id,
                        "gold": 15,
                        "experience": 30
                    }
                ))

        elif quest_state == QuestState.CLOSED:
            self.logger.info("Mayor: Thank you again for keeping our town safe!")

    def on_quest_started(self, event) -> None:
        """Handle quest started events."""
        quest_id = event.data.get("quest_id")
        if quest_id == "goblin_threat_west_haven":
            self._spawn_quest_goblin(quest_id)
            self.logger.info(f"Quest started: {quest_id} - Goblin spawned!")

    def on_quest_completed(self, event) -> None:
        """Handle quest completed events."""
        quest_id = event.data.get("quest_id")
        self.logger.info(f"Quest completed: {quest_id}")

    def on_quest_closed(self, event) -> None:
        """Handle quest closed events."""
        quest_id = event.data.get("quest_id")
        self.logger.info(f"Quest closed: {quest_id} (rewards distributed)")

    def on_objective_completed(self, event) -> None:
        """Handle objective completed events."""
        quest_id = event.data.get("quest_id")
        objective_id = event.data.get("objective_id")
        self.logger.info(f"Objective completed: {objective_id} for quest {quest_id}")
    
    def _spawn_quest_goblin(self, quest_id: str) -> None:
        """Spawn the quest goblin at the designated position."""
        try:
            # Create goblin at position (20, 15) as specified in quest definition
            goblin_position = Position.from_tile_coords(20, 15, 128)  # 128 is tile size
            
            # Use CreateMonsterUseCase to properly initialize AI behaviors
            create_monster_use_case = CreateMonsterUseCase(self.event_bus)
            
            goblin = create_monster_use_case.execute(
                monster_id="goblin_grunt",
                position=goblin_position,
                name="Dangerous Goblin",
                custom_stats={
                    "hp": 35,
                    "max_hp": 35,
                    "strength": 9,
                    "defense": 3,
                    "speed": 6
                },
                custom_id=f"quest_goblin_{quest_id}_{int(time.time())}"
            )

            # Override the ID to match quest system expectations
            goblin.id = f"quest_goblin_{quest_id}_{int(time.time())}"

            # Emit event to spawn the goblin in the game world
            spawn_event = GameEvent(
                event_type="spawn_monster",
                timestamp=time.time(),
                data={
                    "monster": goblin,
                    "quest_id": quest_id
                }
            )
            self.event_bus.publish(spawn_event)

            self.logger.info(f"Spawned quest goblin {goblin.id} at position ({20}, {15}) with AI behaviors initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to spawn quest goblin: {e}")
    
    def _complete_goblin_quest(self) -> None:
        """Mark the goblin quest objective as completed."""
        quest_id = "goblin_threat_west_haven"

        if self.quest_manager.get_quest_state(quest_id) == QuestState.ACTIVE:
            # Complete the defeat_goblin objective
            success = self.quest_manager.complete_objective(quest_id, "defeat_goblin")
            if success:
                self.logger.info("Goblin quest objective completed - return to Mayor for reward")
    
    def get_npc_dialog(self, npc_id: str, npc_position: tuple) -> Optional[str]:
        """Get custom dialog for NPCs based on quest state."""
        if not self.quest_manager:
            return None

        # Handle Mayor dialog with sequential conversation
        if npc_id.startswith("mayor"):
            return self._get_mayor_dialog(npc_id)

        return None

    def _get_mayor_dialog(self, npc_id: str) -> str:
        """Get sequential dialog for the Mayor based on quest state."""
        quest_id = "goblin_threat_west_haven"
        quest_state = self.quest_manager.get_quest_state(quest_id)
        
        # Initialize dialog state if not exists
        if npc_id not in self.npc_dialog_states:
            self.npc_dialog_states[npc_id] = {"dialog_index": 0}

        dialog_state = self.npc_dialog_states[npc_id]
        
        if quest_state == QuestState.NOT_STARTED:
            # Sequential dialog for quest introduction
            mayor_intro_dialog = [
                "Welcome to West Haven, traveler! I am Mayor Aldwin, and I'm glad you've come to our peaceful town.",
                "We have everything a weary adventurer might need - an inn for rest, shops for supplies, and friendly folk.",
                "However, I must tell you about a troubling matter that has come to my attention recently...",
                "One of our citizens spotted a goblin lurking near the town outskirts. This is most unusual and concerning!",
                "Would you be willing to help us deal with this threat? I can offer you 15 gold pieces as a reward."
            ]
            
            current_index = dialog_state.get("dialog_index", 0)
            if current_index < len(mayor_intro_dialog):
                dialog = mayor_intro_dialog[current_index]
                dialog_state["dialog_index"] = current_index + 1
                return dialog
            else:
                # All intro dialog shown, reset for quest acceptance
                dialog_state["dialog_index"] = 0
                return "So, will you help us with this goblin problem?"
                
        elif quest_state == QuestState.ACTIVE:
            return "The goblin is still out there somewhere near the town outskirts. Please be careful, and come back when you've dealt with it."
        elif quest_state == QuestState.COMPLETED:
            return "You've done it! You defeated the goblin! Here's your reward of 15 gold pieces. West Haven is safe once again."
        elif quest_state == QuestState.CLOSED:
            return "Thank you again for keeping our town safe! You're always welcome in West Haven."
        else:
            return "Good day, citizen. West Haven welcomes you."
