"""
Dialog System UI

This module manages the dialog display UI for NPC interactions.
"""

import pygame
from typing import Optional, Dict, Any, Tuple, List
from dataclasses import dataclass

from src.application.interfaces import GameStateData
from src.game_core.config import get_config
from src.editor.ui_config import get_font, get_element_size, get_spacing, scale_value, FONT_LARGE, FONT_NORMAL, FONT_SMALL


@dataclass
class DialogData:
    """Data for a dialog display."""
    npc_name: str
    npc_type: str
    dialog_text: str
    is_visible: bool = False


class DialogUIController:
    """Controller for the dialog display UI."""
    
    def __init__(self):
        """Initialize the dialog UI controller."""
        self.dialog_data: Optional[DialogData] = None
        self.config = get_config()
        
        # UI configuration - Modern blue-grey theme
        self.background_color = (35, 40, 50, 220)    # Blue-grey background with alpha
        self.border_color = (70, 80, 95, 255)        # Blue-grey border
        self.text_color = (240, 245, 250, 255)       # Almost white text
        self.name_color = (120, 180, 255, 255)       # Light blue accent for NPC name
        
        # Layout settings
        self.padding = scale_value(20)
        self.border_width = scale_value(2)
        self.line_spacing = scale_value(5)
        
        # Fonts
        self.name_font = get_font(FONT_LARGE)
        self.text_font = get_font(FONT_NORMAL)
        self.instruction_font = get_font(FONT_SMALL)
        
        # Dialog box dimensions (percentage of screen)
        self.dialog_width_percent = 0.6
        self.dialog_height_percent = 0.25
        
        # Animation/timing
        self.fade_duration = 0.3  # seconds
        self.current_alpha = 0.0
        self.target_alpha = 0.0
        
    def show_dialog(self, npc_name: str, npc_type: str, dialog_text: str) -> None:
        """
        Show a dialog with the given text.
        
        Args:
            npc_name: Name of the NPC
            npc_type: Type of the NPC
            dialog_text: Text to display
        """
        self.dialog_data = DialogData(
            npc_name=npc_name,
            npc_type=npc_type,
            dialog_text=dialog_text,
            is_visible=True
        )
        self.target_alpha = 255.0
        
    def hide_dialog(self) -> None:
        """Hide the dialog."""
        if self.dialog_data:
            self.dialog_data.is_visible = False
        self.target_alpha = 0.0
        
    def is_dialog_visible(self) -> bool:
        """Check if dialog is currently visible."""
        return self.dialog_data is not None and self.dialog_data.is_visible
        
    def handle_event(self, event: pygame.event.Event) -> bool:
        """
        Handle UI events.
        
        Args:
            event: Pygame event
            
        Returns:
            True if event was consumed
        """
        if not self.is_dialog_visible():
            return False
            
        if event.type == pygame.KEYDOWN:
            if event.key in [pygame.K_SPACE, pygame.K_RETURN, pygame.K_ESCAPE]:
                self.hide_dialog()
                return True
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.hide_dialog()
                return True
                
        return False
        
    def update(self, dt: float) -> None:
        """
        Update the dialog UI.
        
        Args:
            dt: Delta time in seconds
        """
        # Update fade animation
        if self.current_alpha != self.target_alpha:
            fade_speed = 255.0 / self.fade_duration
            if self.current_alpha < self.target_alpha:
                self.current_alpha = min(self.target_alpha, self.current_alpha + fade_speed * dt)
            else:
                self.current_alpha = max(self.target_alpha, self.current_alpha - fade_speed * dt)
                
            # Clean up dialog data when fully faded out
            if self.current_alpha <= 0.0 and self.target_alpha <= 0.0:
                self.dialog_data = None
                
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the dialog UI.
        
        Args:
            screen: Surface to render to
        """
        if not self.dialog_data or self.current_alpha <= 0.0:
            return
            
        screen_width, screen_height = screen.get_size()
        
        # Calculate dialog box dimensions and position
        dialog_width = int(screen_width * self.dialog_width_percent)
        dialog_height = int(screen_height * self.dialog_height_percent)
        dialog_x = (screen_width - dialog_width) // 2
        dialog_y = screen_height - dialog_height - scale_value(50)  # Near bottom of screen
        
        # Create dialog surface with alpha
        dialog_surface = pygame.Surface((dialog_width, dialog_height), pygame.SRCALPHA)
        
        # Draw background
        background_color = (*self.background_color[:3], int(self.background_color[3] * self.current_alpha / 255))
        pygame.draw.rect(dialog_surface, background_color, (0, 0, dialog_width, dialog_height))
        
        # Draw border
        border_color = (*self.border_color[:3], int(self.border_color[3] * self.current_alpha / 255))
        pygame.draw.rect(dialog_surface, border_color, (0, 0, dialog_width, dialog_height), self.border_width)
        
        # Render NPC name
        name_text = f"{self.dialog_data.npc_name} ({self.dialog_data.npc_type.title()})"
        name_color = (*self.name_color[:3], int(self.name_color[3] * self.current_alpha / 255))
        name_surface = self.name_font.render(name_text, True, name_color)
        dialog_surface.blit(name_surface, (self.padding, self.padding))
        
        # Render dialog text (with word wrapping)
        text_y = self.padding + name_surface.get_height() + self.line_spacing
        text_area_width = dialog_width - (self.padding * 2)
        text_area_height = dialog_height - text_y - self.padding - scale_value(30)  # Leave space for instructions
        
        wrapped_lines = self._wrap_text(self.dialog_data.dialog_text, text_area_width)
        text_color = (*self.text_color[:3], int(self.text_color[3] * self.current_alpha / 255))
        
        for i, line in enumerate(wrapped_lines):
            if text_y + (i * (self.text_font.get_height() + self.line_spacing)) > text_area_height:
                break  # Don't render text that would overflow
                
            line_surface = self.text_font.render(line, True, text_color)
            dialog_surface.blit(line_surface, (self.padding, text_y + (i * (self.text_font.get_height() + self.line_spacing))))
        
        # Render instructions
        instruction_text = "Press SPACE, ENTER, or click to continue"
        instruction_color = (*self.text_color[:3], int(self.text_color[3] * self.current_alpha / 255 * 0.7))  # Slightly dimmed
        instruction_surface = self.instruction_font.render(instruction_text, True, instruction_color)
        instruction_x = dialog_width - instruction_surface.get_width() - self.padding
        instruction_y = dialog_height - instruction_surface.get_height() - self.padding
        dialog_surface.blit(instruction_surface, (instruction_x, instruction_y))
        
        # Blit dialog to screen
        screen.blit(dialog_surface, (dialog_x, dialog_y))
        
    def _wrap_text(self, text: str, max_width: int) -> List[str]:
        """
        Wrap text to fit within the given width.
        
        Args:
            text: Text to wrap
            max_width: Maximum width in pixels
            
        Returns:
            List of wrapped text lines
        """
        words = text.split(' ')
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + (" " if current_line else "") + word
            test_surface = self.text_font.render(test_line, True, self.text_color)
            
            if test_surface.get_width() <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                    current_line = word
                else:
                    # Single word is too long, just add it anyway
                    lines.append(word)
                    current_line = ""
        
        if current_line:
            lines.append(current_line)
            
        return lines
