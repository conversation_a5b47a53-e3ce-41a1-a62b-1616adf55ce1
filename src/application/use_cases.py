"""
Application Layer Use Cases

Use cases represent the specific actions a user can take in the game.
They orchestrate the flow between the UI and the core game logic.
"""

from typing import Optional, Dict, List, Any, Tuple
import time
import random
import math
from pathlib import Path
import importlib.util

from src.game_core import (
    Player, Monster, NPC, Position, Vector2, Direction, Item,
    is_move_valid, calculate_damage, apply_damage, check_hit,
    PlayerMovedEvent, PlayerAttackedEvent, EntityDamagedEvent, EntityDefeatedEvent,
    update_wandering_entities, find_entities_in_attack_arc, find_entities_in_attack_arc_with_hitboxes, get_weapon_stats
)
from src.game_core.wander_system import get_monsters_ready_to_attack
from src.game_data import get_item_definition
from .interfaces import IEventBus, GameStateData, LevelLayoutData


class CreateMonsterUseCase:
    """Use case for creating monsters with proper stats and behavior configuration."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(
        self,
        monster_id: str,
        position: Position,
        name: Optional[str] = None,
        custom_stats: Optional[Dict[str, int]] = None,
        custom_id: Optional[str] = None,
    ) -> Monster:
        """
        Create a monster from its data ID and apply any overrides.

        Args:
            monster_id: The data ID of the monster to create (e.g., 'horse', 'goblin_grunt').
            position: The position to spawn the monster at.
            name: Optional custom name.
            custom_stats: Optional custom stats overrides.

        Returns:
            The created Monster instance.
        """
        from src.game_data.monsters import get_monster_definition
        from src.game_core.wander_system import WanderBehavior
        from src.game_core import Stats

        monster_def = get_monster_definition(monster_id)
        if not monster_def:
            raise ValueError(f"Unknown monster ID: {monster_id}")

        # Use custom name or default from definition
        monster_name = name if name is not None else monster_def.name

        # Create stats from definition with optional overrides
        stats_data = {
            "hp": monster_def.max_hp,
            "max_hp": monster_def.max_hp,
            "mp": monster_def.max_mp,
            "max_mp": monster_def.max_mp,
            "strength": monster_def.strength,
            "defense": monster_def.defense,
            "speed": monster_def.speed
        }

        if custom_stats:
            stats_data.update(custom_stats)

        monster = Monster(
            id=f"{monster_id}_{position.x}_{position.y}",
            name=monster_name,
            position=position,
            asset_id=monster_def.asset_id,
            stats=Stats(**stats_data),
            monster_type=monster_id,
            ai_behavior=monster_def.ai_behavior,
            experience_reward=monster_def.experience_reward
        )

        # Set up AI behavior for all monsters
        from src.game_core.wander_system import AIBehavior, AIState

        # Initialize AI behavior based on monster type
        if monster_def.ai_behavior in ["peaceful", "skittish"]:
            monster.ai_behavior_instance = AIBehavior(
                enabled=True,
                detection_radius=6.0,
                flee_speed_multiplier=2.0,
                flee_duration=5.0,
                proximity_flee_radius=4.0,
                min_safe_distance=8.0,
                current_state=AIState.WANDERING
            )

            # Also set up wandering behavior for peaceful and skittish animals
            # Get wander configuration from game config if available
            wander_config = None
            try:
                from src.game_core.config import get_config
                config = get_config()
                wander_config = getattr(config, 'wander', None) if hasattr(config, 'wander') else None
            except ImportError:
                pass

            monster.wander_behavior = WanderBehavior(
                enabled=True,
                wander_radius=getattr(wander_config, 'animal_wander_radius', 5.0) if wander_config else 5.0,
                move_speed=getattr(wander_config, 'animal_move_speed_multiplier', 0.8) if wander_config else 0.8,
                idle_time_min=getattr(wander_config, 'animal_idle_time_min', 2.0) if wander_config else 2.0,
                idle_time_max=getattr(wander_config, 'animal_idle_time_max', 8.0) if wander_config else 8.0,
                move_time_min=getattr(wander_config, 'animal_move_time_min', 1.5) if wander_config else 1.5,
                move_time_max=getattr(wander_config, 'animal_move_time_max', 5.0) if wander_config else 5.0
            )
        elif monster_def.ai_behavior == "aggressive":
            monster.ai_behavior_instance = AIBehavior(
                enabled=True,
                detection_radius=6.0,
                chase_speed_multiplier=1.5,
                attack_range=1.5,
                current_state=AIState.WANDERING
            )

        return monster


class CreateNPCUseCase:
    """Use case for creating NPCs from definitions."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(
        self,
        npc_type: str,
        position: Position,
        name: Optional[str] = None,
        custom_dialog: Optional[List[str]] = None,
        custom_inventory: Optional[Dict[str, int]] = None,
    ) -> NPC:
        """
        Create an NPC from its type and apply any overrides.

        Args:
            npc_type: The type of NPC to create (e.g., 'merchant', 'guard').
            position: The position to spawn the NPC at.
            name: Optional custom name.
            custom_dialog: Optional custom dialog.
            custom_inventory: Optional custom inventory.

        Returns:
            The created NPC instance.
        """
        from src.game_data.npcs import NPC_TYPES
        if npc_type not in NPC_TYPES:
            raise ValueError(f"Unknown NPC type: {npc_type}")

        npc_def = NPC_TYPES[npc_type]

        # Use custom name or default from definition
        npc_name = name if name is not None else npc_def.name

        # Use custom dialog or default from definition
        dialog = custom_dialog if custom_dialog is not None else npc_def.default_dialog

        # Use custom inventory or default from definition
        if custom_inventory is not None:
            inventory = custom_inventory
        else:
            # Convert list of item IDs to dictionary with default quantities
            inventory = {item_id: 5 for item_id in npc_def.default_inventory}  # Default 5 of each item

        npc = NPC(
            id=f"{npc_type}_{position.x}_{position.y}",
            name=npc_name,
            position=position,
            asset_id=npc_def.asset_id,
            npc_type=npc_type,
            behavior=npc_def.behavior,
            dialog=dialog,
            inventory=inventory
        )

        # Configure wander behavior based on NPC type
        # Import here to avoid circular dependency
        from src.game_core.wander_system import WanderBehavior
        from src.game_core.config import get_config

        # Get wander configuration
        config = get_config()
        wander_config = getattr(config, 'wander', None)

        # Only commoners should wander - merchants, guards, etc. stay at their posts
        if npc_type == "commoner":
            npc.wander_behavior = WanderBehavior(
                enabled=True,
                wander_radius=getattr(wander_config, 'npc_wander_radius', 4.0) if wander_config else 4.0,
                move_speed=getattr(wander_config, 'npc_move_speed_multiplier', 0.7) if wander_config else 0.7,
                idle_time_min=getattr(wander_config, 'npc_idle_time_min', 3.0) if wander_config else 3.0,
                idle_time_max=getattr(wander_config, 'npc_idle_time_max', 10.0) if wander_config else 10.0,
                move_time_min=getattr(wander_config, 'npc_move_time_min', 2.0) if wander_config else 2.0,
                move_time_max=getattr(wander_config, 'npc_move_time_max', 6.0) if wander_config else 6.0
            )
        else:
            # All other NPC types (merchants, guards, etc.) don't wander
            npc.wander_behavior = WanderBehavior(enabled=False)

        return npc


class MovePlayerUseCase:
    """Use case for moving the player character."""

    def __init__(self, event_bus: IEventBus, tile_size: int = 128, move_speed: float = 200.0):
        """
        Initialize the move use case.

        Args:
            event_bus: Event bus for publishing events
            tile_size: Size of each tile in pixels
            move_speed: Player movement speed in pixels per second
        """
        self.event_bus = event_bus
        self.tile_size = tile_size
        self.move_speed = move_speed

    def _is_colliding(self, pos: Position, player_size: tuple[int, int], game_state: GameStateData) -> bool:
        width, height = player_size
        # Define the four corners of the player's bounding box
        corners = [
            pos,                                      # Top-left
            Position(pos.x + width - 1, pos.y),       # Top-right
            Position(pos.x, pos.y + height - 1),      # Bottom-left
            Position(pos.x + width - 1, pos.y + height - 1) # Bottom-right
        ]

        for corner in corners:
            tile_x, tile_y = corner.to_tile_coords(self.tile_size)

            # Bounds check
            if (tile_y < 0 or tile_y >= len(game_state.collision_map) or
                tile_x < 0 or tile_x >= len(game_state.collision_map[0])):
                return True  # Colliding with map boundaries

            # Check if this tile has dynamic state that affects collision
            tile_key = f"{tile_x},{tile_y}"
            if tile_key in game_state.tile_states:
                tile_state = game_state.tile_states[tile_key]
                # If tile can interact and is open, it's not solid
                if tile_state.get('can_interact', False) and tile_state.get('is_open', False):
                    continue  # This tile is passable

            # Use the collision map for static collision check
            if game_state.collision_map[tile_y][tile_x]:
                return True # Colliding with a wall

        return False

    def execute(self, game_state: GameStateData, direction: Direction, dt: float) -> GameStateData:
        """
        Move the player in the given direction.

        Args:
            game_state: Current game state
            direction: Direction to move the player
            dt: Delta time in seconds

        Returns:
            Updated game state
        """
        if not game_state.player:
            return game_state

        player = game_state.player
        current_pos = player.position
        player_size = player.size

        # Calculate new position based on movement speed and delta time
        distance = self.move_speed * dt
        move_x, move_y = direction.to_vector()

        # Proposed new position
        final_pos = Position(current_pos.x, current_pos.y)

        # Move along X axis
        proposed_x_pos = Position(final_pos.x + move_x * distance, final_pos.y)
        if not self._is_colliding(proposed_x_pos, player_size, game_state):
            final_pos = proposed_x_pos

        # Move along Y axis
        proposed_y_pos = Position(final_pos.x, final_pos.y + move_y * distance)
        if not self._is_colliding(proposed_y_pos, player_size, game_state):
            final_pos = proposed_y_pos

        if final_pos == current_pos:
            return game_state # No change in position

        # Create updated player
        updated_player = Player(
            id=player.id,
            name=player.name,
            position=final_pos,
            asset_id=player.asset_id,
            stats=player.stats,
            size=player.size,
            level=player.level,
            experience=player.experience,
            head_equipment=player.head_equipment,
            chest_equipment=player.chest_equipment,
            legs_equipment=player.legs_equipment,
            boots_equipment=player.boots_equipment,
            main_hand_weapon=player.main_hand_weapon,
            off_hand_equipment=player.off_hand_equipment,
            inventory=player.inventory,
            inventory_max_size=player.inventory_max_size
        )

        # Update game state
        new_game_state = GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            npcs=game_state.npcs.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles,
            tile_states=game_state.tile_states.copy()
        )

        # Emit event
        event = PlayerMovedEvent(
            player_id=player.id,
            old_position=current_pos,
            new_position=final_pos,
            timestamp=time.time()
        )
        self.event_bus.publish(event)

        return new_game_state


class PlayerAttackUseCase:
    """Use case for player attacking."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus
        from src.infrastructure.logging.game_logger import get_logger
        self.logger = get_logger(__name__)
    
    def execute(self, game_state: GameStateData, direction_vector: Vector2) -> Tuple[GameStateData, List[str]]:
        """
        Execute player attack using weapon stats and arc-based damage.

        Args:
            game_state: Current game state
            direction_vector: Normalized direction vector for the attack

        Returns:
            Tuple of (updated game state, list of attacked monster IDs)
        """
        if not game_state.player:
            return game_state, []

        player = game_state.player

        # Get weapon stats
        weapon_damage = 1  # Base unarmed damage
        weapon_range_tiles = 1.0  # Base unarmed range in tiles
        damage_arc = 60.0  # Base unarmed arc

        if player.main_hand_weapon:
            weapon_def = get_item_definition(player.main_hand_weapon)
            if weapon_def and weapon_def.properties:
                weapon_damage, _, weapon_range_tiles, damage_arc = get_weapon_stats(weapon_def.properties)

        # Convert weapon range from tiles to pixels (tile_size = 128)
        tile_size = 128  # TODO: Get this from config
        weapon_range_pixels = weapon_range_tiles * tile_size

        # Emit attack event first
        attack_event = PlayerAttackedEvent(
            player_id=player.id,
            direction_vector=direction_vector,
            attack_power=player.stats.strength + weapon_damage,
            timestamp=time.time()
        )
        self.event_bus.publish(attack_event)

        # Find targets in attack arc using hitbox-aware detection
        player_pos = player.position

        # Define default entity size for monsters (they don't have size attribute yet)
        DEFAULT_ENTITY_SIZE = (64, 64)  # width, height in pixels

        # Prepare entity data with positions and sizes
        entity_data = []
        for monster_id, monster in game_state.monsters.items():
            entity_data.append((monster_id, monster.position, DEFAULT_ENTITY_SIZE))

        targets_in_arc = find_entities_in_attack_arc_with_hitboxes(
            player_pos, direction_vector, weapon_range_pixels, damage_arc, entity_data
        )

        updated_monsters = game_state.monsters.copy()

        for monster_id in targets_in_arc:
            monster = game_state.monsters[monster_id]

            # Check if attack hits
            if check_hit(player.stats, monster.stats):
                # Calculate damage with weapon bonus
                damage = calculate_damage(player.stats, monster.stats, weapon_damage)

                # Apply damage
                new_stats = apply_damage(monster.stats, damage)

                # Create updated monster
                updated_monster = Monster(
                    id=monster.id,
                    name=monster.name,
                    position=monster.position,
                    asset_id=monster.asset_id,
                    stats=new_stats,
                    monster_type=monster.monster_type,
                    ai_behavior=monster.ai_behavior,
                    experience_reward=monster.experience_reward
                )

                # Preserve behaviors
                if hasattr(monster, 'wander_behavior'):
                    updated_monster.wander_behavior = monster.wander_behavior
                if hasattr(monster, 'ai_behavior_instance'):
                    updated_monster.ai_behavior_instance = monster.ai_behavior_instance

                updated_monsters[monster_id] = updated_monster

                # Emit damage event
                damage_event = EntityDamagedEvent(
                    target_id=monster_id,
                    damage=damage,
                    timestamp=time.time(),
                    source_id=player.id
                )
                self.event_bus.publish(damage_event)

                # Check if monster is defeated
                if new_stats.hp <= 0:
                    defeat_event = EntityDefeatedEvent(
                        entity_id=monster_id,
                        entity_type="monster",
                        timestamp=time.time(),
                        killer_id=player.id
                    )
                    self.event_bus.publish(defeat_event)

                    # Add loot directly to player inventory
                    try:
                        from src.game_data.monsters import generate_loot
                        loot = generate_loot(monster.monster_type)

                        self.logger.info(f"Generated loot from {monster.name} ({monster.monster_type}): {loot}")

                        # Add each loot item to player inventory
                        updated_player = game_state.player
                        for item_id, quantity in loot.items():
                            try:
                                updated_player = updated_player.add_item_to_inventory(item_id, quantity)
                                self.logger.info(f"Player received {quantity}x {item_id} from {monster.name}")
                            except ValueError as e:
                                # Inventory full - skip this item
                                self.logger.warning(f"Inventory full - couldn't add {quantity}x {item_id}: {e}")

                        # Update game state with new player inventory
                        game_state = GameStateData(
                            player=updated_player,
                            monsters=updated_monsters,
                            items=game_state.items.copy(),
                            npcs=game_state.npcs.copy(),
                            current_level_id=game_state.current_level_id,
                            collision_map=game_state.collision_map,
                            level_tiles=game_state.level_tiles,
                            tile_states=game_state.tile_states.copy()
                        )
                    except ImportError as e:
                        # Loot system not available, continue without loot
                        self.logger.warning(f"Loot system not available: {e}")
                    except Exception as e:
                        # Any other error with loot system
                        self.logger.error(f"Error generating loot: {e}")

                    # Remove defeated monster from the game state
                    del updated_monsters[monster_id]

        # Return updated game state and list of attacked monsters
        updated_game_state = GameStateData(
            player=game_state.player,
            monsters=updated_monsters,
            items=game_state.items,  # This may have been updated by loot spawning
            npcs=game_state.npcs,
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles,
            tile_states=game_state.tile_states
        )

        return updated_game_state, targets_in_arc


class MonsterAttackUseCase:
    """Use case for monsters attacking the player."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus
        from src.infrastructure.logging.game_logger import get_logger
        self.logger = get_logger(__name__)

    def execute(self, game_state: GameStateData, attacking_monster_ids: List[str]) -> GameStateData:
        """
        Execute monster attacks on the player.

        Args:
            game_state: Current game state
            attacking_monster_ids: List of monster IDs that are attacking

        Returns:
            Updated game state with player damage applied
        """
        if not game_state.player or not attacking_monster_ids:
            return game_state

        player = game_state.player
        updated_player = player

        for monster_id in attacking_monster_ids:
            if monster_id not in game_state.monsters:
                continue

            monster = game_state.monsters[monster_id]

            # Check if attack hits
            if check_hit(monster.stats, player.stats):
                # Calculate damage
                damage = calculate_damage(monster.stats, player.stats, 0)  # No weapon bonus for monsters

                # Apply damage to player
                new_stats = apply_damage(player.stats, damage)

                # Create updated player
                updated_player = Player(
                    id=player.id,
                    name=player.name,
                    position=player.position,
                    asset_id=player.asset_id,
                    stats=new_stats,
                    size=player.size,
                    level=player.level,
                    experience=player.experience,
                    head_equipment=player.head_equipment,
                    chest_equipment=player.chest_equipment,
                    legs_equipment=player.legs_equipment,
                    boots_equipment=player.boots_equipment,
                    main_hand_weapon=player.main_hand_weapon,
                    off_hand_equipment=player.off_hand_equipment,
                    inventory=player.inventory,
                    inventory_max_size=player.inventory_max_size
                )

                # Emit damage event
                damage_event = EntityDamagedEvent(
                    target_id=player.id,
                    damage=damage,
                    timestamp=time.time(),
                    source_id=monster_id
                )
                self.event_bus.publish(damage_event)

                self.logger.info(f"{monster.name} attacked {player.name} for {damage} damage!")

                # Check if player is defeated
                if new_stats.hp <= 0:
                    defeat_event = EntityDefeatedEvent(
                        entity_id=player.id,
                        entity_type="player",
                        timestamp=time.time(),
                        killer_id=monster_id
                    )
                    self.event_bus.publish(defeat_event)
                    self.logger.info(f"{player.name} was defeated by {monster.name}!")

        # Return updated game state
        return GameStateData(
            player=updated_player,
            monsters=game_state.monsters,
            items=game_state.items,
            npcs=game_state.npcs,
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles,
            tile_states=game_state.tile_states.copy() if hasattr(game_state, 'tile_states') and game_state.tile_states else {},
            quest_data=game_state.quest_data.copy() if hasattr(game_state, 'quest_data') and game_state.quest_data else {}
        )


class BuildLevelUseCase:
    """Use case for building a level from layout data."""
    
    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus
        self._level_configs = {}

    def _get_level_config(self, level_id: str) -> Optional[Dict[str, Any]]:
        if level_id in self._level_configs:
            return self._level_configs[level_id]

        try:
            level_config_path = Path(f"levels/{level_id}/level_config.py")
            if not level_config_path.exists():
                self._level_configs[level_id] = None
                return None

            spec = importlib.util.spec_from_file_location(f"levels.{level_id}.level_config", level_config_path)
            level_config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(level_config_module)
            
            config = {
                "npc_overrides": getattr(level_config_module, "npc_overrides", {}),
                "spawn_overrides": getattr(level_config_module, "spawn_overrides", {}),
                "level_events": getattr(level_config_module, "level_events", {}),
            }
            self._level_configs[level_id] = config
            return config
        except Exception as e:
            print(f"Error loading level config for {level_id}: {e}")
            self._level_configs[level_id] = None
            return None

    def _get_npc_overrides(self, level_id: str, npc_type: str, tile_x: int, tile_y: int) -> Dict[str, Any]:
        """Get NPC overrides from level configuration."""
        config = self._get_level_config(level_id)
        if not config or "npc_overrides" not in config:
            return {}

        npc_overrides = config["npc_overrides"]
        overrides = {}

        # Type-based overrides (e.g., all merchants)
        inventory_key = f"{npc_type}_inventory"
        if inventory_key in npc_overrides:
            overrides["inventory"] = npc_overrides[inventory_key]

        dialog_key = f"{npc_type}_dialog"
        if dialog_key in npc_overrides:
            overrides["dialog"] = npc_overrides[dialog_key]

        # Position-based overrides for specific NPCs
        specific_npc_key = f"{npc_type}_at_{tile_x}_{tile_y}"
        if "specific_npcs" in npc_overrides and specific_npc_key in npc_overrides["specific_npcs"]:
            specific_overrides = npc_overrides["specific_npcs"][specific_npc_key]
            overrides.update(specific_overrides)

        return overrides
    
    def execute(self, layout_data: "LevelLayoutData", level_id: str) -> GameStateData:
        """
        Build a game state from level layout data.
        
        Args:
            layout_data: The level layout data
            level_id: ID of the level being built
        
        Returns:
            New game state for the level
        """
        
        # Initialize empty game state
        player = None
        monsters = {}
        items = {}
        npcs = {}
        
        # Process entities from layout data
        for entity_data in layout_data.entities:
            entity_type = entity_data.get("type")
            # Convert tile coordinates to pixel coordinates (centered in tile)
            tile_x = entity_data.get("x", 0) 
            tile_y = entity_data.get("y", 0)
            position = Position.from_tile_coords(tile_x, tile_y, 128)  # 128 is tile_size
            
            if entity_type == "player":
                # Use CreateNewPlayerUseCase to set up player with starting equipment
                create_player_use_case = CreateNewPlayerUseCase(self.event_bus)
                player = create_player_use_case.execute("Hero", position)
                # Override asset_id if specified in entity data
                if "asset_id" in entity_data:
                    player = Player(
                        id=player.id,
                        name=player.name,
                        position=player.position,
                        asset_id=entity_data["asset_id"],
                        stats=player.stats,
                        size=player.size,
                        level=player.level,
                        experience=player.experience,
                        head_equipment=player.head_equipment,
                        chest_equipment=player.chest_equipment,
                        legs_equipment=player.legs_equipment,
                        boots_equipment=player.boots_equipment,
                        main_hand_weapon=player.main_hand_weapon,
                        off_hand_equipment=player.off_hand_equipment,
                        inventory=player.inventory,
                        inventory_max_size=player.inventory_max_size
                    )
            
            elif entity_type == "monster":
                # Create monster using CreateMonsterUseCase
                data_id = entity_data.get("data_id", "goblin_grunt")
                create_monster_use_case = CreateMonsterUseCase(self.event_bus)

                # Get custom name and stats from entity data
                custom_name = entity_data.get("name")
                custom_stats = {}

                # Extract any stat overrides from entity data
                for stat_name in ["hp", "max_hp", "mp", "max_mp", "strength", "defense", "speed"]:
                    if stat_name in entity_data:
                        custom_stats[stat_name] = entity_data[stat_name]

                monster = create_monster_use_case.execute(
                    monster_id=data_id,
                    position=position,
                    name=custom_name,
                    custom_stats=custom_stats if custom_stats else None
                )
                monsters[monster.id] = monster

            elif entity_type == "npc":
                # Create NPC using CreateNPCUseCase
                data_id = entity_data.get("data_id", "commoner")
                create_npc_use_case = CreateNPCUseCase(self.event_bus)

                # Get custom name, dialog, and inventory from entity data
                custom_name = entity_data.get("name")
                # Check for both "dialog" and "dialogue" (backward compatibility)
                custom_dialog = entity_data.get("dialog") or entity_data.get("dialogue")
                # Also check in properties section (backward compatibility)
                if not custom_dialog and "properties" in entity_data:
                    custom_dialog = entity_data["properties"].get("dialog") or entity_data["properties"].get("dialogue")
                custom_inventory = entity_data.get("inventory")

                # Apply level-specific NPC overrides
                level_overrides = self._get_npc_overrides(level_id, data_id, tile_x, tile_y)
                if level_overrides:
                    if not custom_name and "name" in level_overrides:
                        custom_name = level_overrides["name"]
                    if not custom_dialog and "dialog" in level_overrides:
                        custom_dialog = level_overrides["dialog"]
                    if not custom_inventory and "inventory" in level_overrides:
                        custom_inventory = level_overrides["inventory"]

                npc = create_npc_use_case.execute(
                    npc_type=data_id,
                    position=position,
                    name=custom_name,
                    custom_dialog=custom_dialog,
                    custom_inventory=custom_inventory
                )

                # Note: use_sequential_dialog is no longer used - all NPCs use sequential dialog by default

                # Override asset_id if specified
                if "asset_id" in entity_data:
                    # Preserve wander_behavior when creating new NPC
                    old_wander_behavior = npc.wander_behavior
                    npc = NPC(
                        id=npc.id,
                        name=npc.name,
                        position=npc.position,
                        asset_id=entity_data["asset_id"],
                        npc_type=npc.npc_type,
                        behavior=npc.behavior,
                        dialog=npc.dialog,
                        inventory=npc.inventory,
                        properties=npc.properties
                    )
                    # Restore wander behavior
                    npc.wander_behavior = old_wander_behavior

                npcs[npc.id] = npc
        
        # Create game state
        game_state = GameStateData(
            player=player,
            monsters=monsters,
            items=items,
            npcs=npcs,
            current_level_id=level_id,
            collision_map=layout_data.collision_map,
            level_tiles=layout_data.tiles,
            tile_states={},  # Initialize empty tile states for new levels
            quest_data={}  # Initialize empty quest data for new levels
        )

        return game_state


class EquipItemUseCase:
    """Use case for equipping items from inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, item_id: str, slot: str) -> GameStateData:
        """
        Equip an item from inventory to an equipment slot.

        Args:
            game_state: Current game state
            item_id: ID of the item to equip
            slot: Equipment slot to equip to

        Returns:
            Updated game state with item equipped
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        # Validate item exists in game data
        try:
            from src.game_data.items import ITEMS
            if item_id not in ITEMS:
                raise ValueError(f"Unknown item: {item_id}")

            item_def = ITEMS[item_id]

            # Validate item can be equipped in this slot
            item_slot = item_def.properties.get("slot")
            # Map item slot names to player equipment slot names
            slot_mapping = {
                "head": "head_equipment",
                "chest": "chest_equipment",
                "legs": "legs_equipment",
                "boots": "boots_equipment",
                "main_hand": "main_hand_weapon",
                "off_hand": "off_hand_equipment",
                # Handle both old and new slot naming conventions
                "head_equipment": "head_equipment",
                "chest_equipment": "chest_equipment",
                "legs_equipment": "legs_equipment",
                "boots_equipment": "boots_equipment",
                "main_hand_weapon": "main_hand_weapon",
                "off_hand_equipment": "off_hand_equipment"
            }
            expected_slot = slot_mapping.get(item_slot, item_slot)
            if expected_slot != slot:
                raise ValueError(f"Item '{item_id}' cannot be equipped in slot '{slot}' (requires '{expected_slot}')")

        except ImportError:
            # Fallback if items module not available
            pass

        # Equip the item
        updated_player = game_state.player.equip_item(item_id, slot)

        # Create updated game state
        new_game_state = GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles
        )

        # TODO: Emit equipment changed event

        return new_game_state


class UnequipItemUseCase:
    """Use case for unequipping items to inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, slot: str) -> GameStateData:
        """
        Unequip an item from an equipment slot to inventory.

        Args:
            game_state: Current game state
            slot: Equipment slot to unequip from

        Returns:
            Updated game state with item unequipped
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        # Unequip the item
        updated_player = game_state.player.unequip_item(slot)

        # Create updated game state
        new_game_state = GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles
        )

        # TODO: Emit equipment changed event

        return new_game_state


class GetInventoryUseCase:
    """Use case for retrieving player inventory data."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData) -> Dict[str, Any]:
        """
        Get current player inventory information.

        Args:
            game_state: Current game state

        Returns:
            Dictionary containing inventory data
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        player = game_state.player

        return {
            "inventory": player.inventory.copy(),
            "max_size": player.inventory_max_size,
            "used_slots": player.get_inventory_space_used(),
            "remaining_slots": player.get_inventory_space_remaining()
        }


class GetPlayerVisualDataUseCase:
    """Use case for retrieving player visual data for rendering."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData) -> Dict[str, Any]:
        """
        Get player visual data including equipment for rendering.

        Args:
            game_state: Current game state

        Returns:
            Dictionary containing visual data
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        player = game_state.player

        return {
            "equipment": player.get_equipment_dict(),
            "position": player.position,
            "asset_id": player.asset_id,
            "stats": player.stats
        }


class AddItemToInventoryUseCase:
    """Use case for adding items to player inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, item_id: str, quantity: int = 1) -> GameStateData:
        """
        Add an item to player inventory.

        Args:
            game_state: Current game state
            item_id: ID of the item to add
            quantity: Quantity to add

        Returns:
            Updated game state with item added
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        # Add item to inventory
        updated_player = game_state.player.add_item_to_inventory(item_id, quantity)

        # Create updated game state
        new_game_state = GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            npcs=game_state.npcs.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles,
            tile_states=game_state.tile_states.copy()
        )

        return new_game_state


class RemoveItemFromInventoryUseCase:
    """Use case for removing items from player inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, item_id: str, quantity: int = 1) -> GameStateData:
        """
        Remove an item from player inventory.

        Args:
            game_state: Current game state
            item_id: ID of the item to remove
            quantity: Quantity to remove

        Returns:
            Updated game state with item removed
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        # Remove item from inventory
        updated_player = game_state.player.remove_item_from_inventory(item_id, quantity)

        # Create updated game state
        new_game_state = GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles
        )

        return new_game_state


class RearrangeInventoryUseCase:
    """Use case for rearranging items within the player's inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, source_index: int, target_index: int) -> GameStateData:
        """
        Rearrange items within the inventory by swapping positions.

        Args:
            game_state: Current game state
            source_index: Index of the item to move
            target_index: Index where to move the item

        Returns:
            Updated game state with rearranged inventory
        """
        if not game_state.player:
            return game_state

        player = game_state.player
        inventory_items = list(player.inventory.items())

        # Validate indices
        if source_index < 0 or source_index >= len(inventory_items):
            return game_state
        if target_index < 0:
            return game_state

        # If target index is beyond current inventory, just move to end
        if target_index >= len(inventory_items):
            target_index = len(inventory_items) - 1

        # If source and target are the same, no change needed
        if source_index == target_index:
            return game_state

        # Get the items to swap
        source_item = inventory_items[source_index]

        # Create new inventory with items rearranged
        new_inventory_list = inventory_items.copy()

        # Remove the source item
        removed_item = new_inventory_list.pop(source_index)

        # Insert it at the target position
        new_inventory_list.insert(target_index, removed_item)

        # Convert back to dictionary
        new_inventory = dict(new_inventory_list)

        # Create updated player
        updated_player = Player(
            id=player.id,
            name=player.name,
            position=player.position,
            asset_id=player.asset_id,
            stats=player.stats,
            size=player.size,
            level=player.level,
            experience=player.experience,
            head_equipment=player.head_equipment,
            chest_equipment=player.chest_equipment,
            legs_equipment=player.legs_equipment,
            boots_equipment=player.boots_equipment,
            main_hand_weapon=player.main_hand_weapon,
            off_hand_equipment=player.off_hand_equipment,
            inventory=new_inventory,
            inventory_max_size=player.inventory_max_size
        )

        # Return updated game state
        return GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles
        )


class AddItemToInventoryUseCase:
    """Use case for adding items to player inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, item_id: str, quantity: int = 1) -> GameStateData:
        """
        Add an item to the player's inventory.

        Args:
            game_state: Current game state
            item_id: ID of the item to add
            quantity: Quantity to add

        Returns:
            Updated game state with item added
        """
        if not game_state.player:
            return game_state

        try:
            updated_player = game_state.player.add_item_to_inventory(item_id, quantity)
        except ValueError:
            # Inventory full or other error
            return game_state

        # Return updated game state
        return GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            npcs=game_state.npcs.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles,
            tile_states=game_state.tile_states.copy(),
            quest_data=game_state.quest_data.copy() if hasattr(game_state, 'quest_data') and game_state.quest_data else {}
        )


class TileInteractionUseCase:
    """Use case for handling tile interactions (doors, switches, etc.)."""

    def __init__(self, event_bus: IEventBus, map_parser):
        self.event_bus = event_bus
        self.map_parser = map_parser

    def execute(self, game_state: GameStateData, tile_x: int, tile_y: int) -> GameStateData:
        """
        Interact with a tile at the given coordinates.

        Args:
            game_state: Current game state
            tile_x: X coordinate of the tile
            tile_y: Y coordinate of the tile

        Returns:
            Updated game state with tile interaction applied
        """
        if not game_state.level_tiles or not game_state.collision_map:
            return game_state

        # Check bounds
        if (tile_y < 0 or tile_y >= len(game_state.level_tiles) or
            tile_x < 0 or tile_x >= len(game_state.level_tiles[0])):
            return game_state

        # Get tile position key
        tile_key = f"{tile_x},{tile_y}"

        # Get current tile asset ID
        current_asset_id = game_state.level_tiles[tile_y][tile_x]

        # Check if this tile has a state (which means it's interactive)
        current_state = game_state.tile_states.get(tile_key, {})
        if current_state.get('can_interact', False):
            # This tile is already known to be interactive, get the stored base definition
            tile_def = current_state.get('base_tile_def')
            if not tile_def:
                # Fallback: try to find the base definition
                tile_def = self._get_base_tile_definition_for_interactive_tile(current_asset_id)
        else:
            # Check base legend for interactive tiles
            tile_def = self._get_base_tile_definition_for_interactive_tile(current_asset_id)
            if not tile_def or not tile_def.get('properties', {}).get('can_interact', False):
                return game_state  # Tile is not interactive

        # Get current tile state
        current_state = game_state.tile_states.get(tile_key, {})

        # Determine current open state - check both the tile state and the current asset ID
        state_says_open = current_state.get('is_open', False)
        asset_says_open = current_asset_id == tile_def.get('properties', {}).get('open_asset_id')

        # Use the tile state if it exists, otherwise infer from asset ID
        if tile_key in game_state.tile_states:
            is_currently_open = state_says_open
        else:
            is_currently_open = asset_says_open

        # Toggle the state
        new_is_open = not is_currently_open

        # Update tile states
        new_tile_states = game_state.tile_states.copy()
        new_tile_states[tile_key] = {
            'is_open': new_is_open,
            'can_interact': True,
            'base_tile_def': tile_def  # Store the base tile definition for future interactions
        }

        # Update level tiles with new asset ID
        new_level_tiles = [row[:] for row in game_state.level_tiles]  # Deep copy
        if new_is_open:
            new_asset_id = tile_def.get('properties', {}).get('open_asset_id', current_asset_id)
        else:
            new_asset_id = tile_def.get('properties', {}).get('closed_asset_id', current_asset_id)

        new_level_tiles[tile_y][tile_x] = new_asset_id

        # Update collision map
        new_collision_map = [row[:] for row in game_state.collision_map]  # Deep copy
        new_collision_map[tile_y][tile_x] = not new_is_open  # Open tiles are not solid

        # Emit tile interaction event
        from src.game_core.events import TileInteractionEvent
        event = TileInteractionEvent(
            tile_x=tile_x,
            tile_y=tile_y,
            tile_type=tile_def.get('type', 'unknown'),
            new_state={'is_open': new_is_open},
            timestamp=time.time()
        )
        self.event_bus.publish(event)

        # Return updated game state
        return GameStateData(
            player=game_state.player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            npcs=game_state.npcs.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=new_collision_map,
            level_tiles=new_level_tiles,
            tile_states=new_tile_states
        )

    def _get_tile_definition(self, asset_id: str) -> Optional[Dict[str, Any]]:
        """Get tile definition from base legend by asset ID."""
        for symbol, definition in self.map_parser.base_legend.items():
            if (definition.get('category') == 'tile' and
                definition.get('asset_id') == asset_id):
                return definition
        return None

    def _get_base_tile_definition_for_interactive_tile(self, current_asset_id: str) -> Optional[Dict[str, Any]]:
        """Get the base tile definition for an interactive tile, handling both open and closed states."""
        
        # First try to find by current asset ID
        tile_def = self._get_tile_definition(current_asset_id)
        if tile_def and tile_def.get('properties', {}).get('can_interact', False):
            return tile_def

        # If not found, check if this is an open door asset and find the corresponding closed door
        if current_asset_id == "tile.door.wooden.open":
            closed_def = self._get_tile_definition("tile.door.wooden")
            return closed_def

        # Also check the reverse - find any tile definition that has this asset as its open_asset_id
        for symbol, definition in self.map_parser.base_legend.items():
            if (definition.get('category') == 'tile' and
                definition.get('properties', {}).get('can_interact', False)):
                open_asset = definition.get('properties', {}).get('open_asset_id')
                # Check if this tile's open asset matches our current asset
                if open_asset == current_asset_id:
                    return definition

        return None


class RemoveItemFromInventoryUseCase:
    """Use case for removing items from player inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, item_id: str, quantity: int = 1) -> GameStateData:
        """
        Remove an item from the player's inventory.

        Args:
            game_state: Current game state
            item_id: ID of the item to remove
            quantity: Quantity to remove

        Returns:
            Updated game state with item removed
        """
        if not game_state.player:
            return game_state

        try:
            updated_player = game_state.player.remove_item_from_inventory(item_id, quantity)
        except ValueError:
            # Item not found or insufficient quantity
            return game_state

        # Return updated game state
        return GameStateData(
            player=updated_player,
            monsters=game_state.monsters.copy(),
            items=game_state.items.copy(),
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles
        )


class GetInventoryUseCase:
    """Use case for getting player inventory information."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData) -> dict:
        """
        Get the player's current inventory.

        Args:
            game_state: Current game state

        Returns:
            Dictionary with inventory information
        """
        if not game_state.player:
            return {"inventory": {}, "space_used": 0, "space_remaining": 0}

        player = game_state.player
        return {
            "inventory": player.inventory.copy(),
            "space_used": player.get_inventory_space_used(),
            "space_remaining": player.get_inventory_space_remaining()
        }


class CanAddItemToInventoryUseCase:
    """Use case for checking if an item can be added to inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, item_id: str, quantity: int = 1) -> bool:
        """
        Check if an item can be added to the player's inventory.

        Args:
            game_state: Current game state
            item_id: ID of the item to check
            quantity: Quantity to check

        Returns:
            True if item can be added, False otherwise
        """
        if not game_state.player:
            return False

        player = game_state.player

        # If item already exists in inventory, we can always add more
        if item_id in player.inventory:
            return True

        # If item doesn't exist, check if we have space for a new item type
        return player.get_inventory_space_remaining() > 0


class GetPlayerVisualDataUseCase:
    """Use case for getting player visual data for rendering."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData) -> dict:
        """
        Get player's current equipment IDs for rendering.

        Args:
            game_state: Current game state

        Returns:
            Dictionary with equipment IDs for rendering
        """
        if not game_state.player:
            return {}

        player = game_state.player
        return {
            "head_equipment": player.head_equipment,
            "chest_equipment": player.chest_equipment,
            "legs_equipment": player.legs_equipment,
            "boots_equipment": player.boots_equipment,
            "main_hand_weapon": player.main_hand_weapon,
            "off_hand_equipment": player.off_hand_equipment
        }


class CreateNewPlayerUseCase:
    """Use case for creating a new player with starting equipment and inventory."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, name: str, starting_position: Position) -> Player:
        """
        Create a new player with default starting equipment and inventory.

        Args:
            name: Player name
            starting_position: Starting position for the player

        Returns:
            New player with starting equipment and inventory
        """
        # Import here to avoid circular imports
        from src.game_data.items import DEFAULT_STARTING_EQUIPMENT, DEFAULT_STARTING_INVENTORY
        from src.game_core import Stats
        from src.game_core.config import get_config

        # Create base player
        player = Player(
            id="player",
            name=name,
            position=starting_position,
            asset_id="player.hero",
            stats=Stats(
                hp=100, max_hp=100, mp=50, max_mp=50,
                strength=10, defense=5, speed=8
            ),
            size=(64, 64),  # Default player size
            level=1,
            experience=0,
            # Set starting equipment
            head_equipment=DEFAULT_STARTING_EQUIPMENT.get("head_equipment"),
            chest_equipment=DEFAULT_STARTING_EQUIPMENT.get("chest_equipment"),
            legs_equipment=DEFAULT_STARTING_EQUIPMENT.get("legs_equipment"),
            boots_equipment=DEFAULT_STARTING_EQUIPMENT.get("boots_equipment"),
            main_hand_weapon=DEFAULT_STARTING_EQUIPMENT.get("main_hand_weapon"),
            off_hand_equipment=DEFAULT_STARTING_EQUIPMENT.get("off_hand_equipment"),
            # Set starting inventory
            inventory=DEFAULT_STARTING_INVENTORY.copy(),
            inventory_max_size=get_config().inventory_ui.inventory_max_slots
        )

        return player


class GetPlayerStatsUseCase:
    """Use case for retrieving player stats including equipment bonuses."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData) -> Dict[str, Any]:
        """
        Get player stats including equipment bonuses.

        Args:
            game_state: Current game state

        Returns:
            Dictionary containing player stats and equipment bonuses
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        player = game_state.player

        # Get base stats and total stats (with equipment bonuses)
        base_stats = player.stats
        total_stats = player.get_total_stats()
        equipment_bonuses = player.get_equipment_bonuses()

        return {
            "base_stats": {
                "hp": base_stats.hp,
                "max_hp": base_stats.max_hp,
                "mp": base_stats.mp,
                "max_mp": base_stats.max_mp,
                "strength": base_stats.strength,
                "defense": base_stats.defense,
                "speed": base_stats.speed
            },
            "total_stats": {
                "hp": total_stats.hp,
                "max_hp": total_stats.max_hp,
                "mp": total_stats.mp,
                "max_mp": total_stats.max_mp,
                "strength": total_stats.strength,
                "defense": total_stats.defense,
                "speed": total_stats.speed
            },
            "equipment_bonuses": equipment_bonuses,
            "level": player.level,
            "experience": player.experience,
            "name": player.name
        }


class InteractWithNPCUseCase:
    """Use case for player interaction with NPCs."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, npc_id: str) -> Dict[str, Any]:
        """
        Execute NPC interaction.

        Args:
            game_state: Current game state
            npc_id: ID of the NPC to interact with

        Returns:
            Dictionary containing interaction result and data
        """
        if npc_id not in game_state.npcs:
            raise ValueError(f"NPC '{npc_id}' not found in game state")

        npc = game_state.npcs[npc_id]

        # Get dialog text using unified sequential system
        from src.game_core.dialog_manager import get_dialog_manager
        dialog_manager = get_dialog_manager()
        current_index = dialog_manager.get_dialog_index(npc_id)
        dialog_text, next_index = npc.get_dialog(current_index)
        dialog_manager.set_dialog_index(npc_id, next_index)

        if npc.has_store_behavior():
            return {
                "interaction_type": "store",
                "npc_id": npc_id,
                "npc_name": npc.name,
                "npc_type": npc.npc_type,
                "inventory": npc.inventory,
                "dialog": dialog_text
            }
        elif npc.has_dialog_behavior():
            return {
                "interaction_type": "dialog",
                "npc_id": npc_id,
                "npc_name": npc.name,
                "npc_type": npc.npc_type,
                "dialog": dialog_text
            }
        else:
            return {
                "interaction_type": "none",
                "npc_id": npc_id,
                "npc_name": npc.name,
                "dialog": f"{npc.name} doesn't seem interested in talking."
            }


class BuyFromNPCUseCase:
    """Use case for buying items from NPC stores."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, npc_id: str, item_id: str, quantity: int = 1) -> GameStateData:
        """
        Execute item purchase from NPC.

        Args:
            game_state: Current game state
            npc_id: ID of the NPC to buy from
            item_id: ID of the item to buy
            quantity: Quantity to buy

        Returns:
            Updated game state
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        if npc_id not in game_state.npcs:
            raise ValueError(f"NPC '{npc_id}' not found in game state")

        npc = game_state.npcs[npc_id]

        if not npc.has_store_behavior():
            raise ValueError(f"NPC '{npc.name}' is not a merchant")

        if not npc.has_item_in_stock(item_id, quantity):
            raise ValueError(f"NPC doesn't have enough '{item_id}' in stock")

        # Get item price (for now, use a simple pricing system)
        try:
            from src.game_data.items import ITEMS
            if item_id not in ITEMS:
                raise ValueError(f"Item '{item_id}' not found in item definitions")

            item_def = ITEMS[item_id]
            total_cost = item_def.value * quantity

            # Check if player has enough gold
            player_gold = game_state.player.inventory.get("gold_coin", 0)
            if player_gold < total_cost:
                raise ValueError(f"Not enough gold (need {total_cost}, have {player_gold})")

            # Execute transaction
            updated_player = game_state.player.remove_item_from_inventory("gold_coin", total_cost)
            updated_player = updated_player.add_item_to_inventory(item_id, quantity)

            updated_npc = npc.remove_item_from_stock(item_id, quantity)

            # Update game state
            new_npcs = game_state.npcs.copy()
            new_npcs[npc_id] = updated_npc

            return GameStateData(
                player=updated_player,
                monsters=game_state.monsters,
                items=game_state.items,
                npcs=new_npcs,
                current_level_id=game_state.current_level_id,
                collision_map=game_state.collision_map,
                level_tiles=game_state.level_tiles
            )

        except ImportError:
            raise ValueError("Item definitions not available")


class SellToNPCUseCase:
    """Use case for selling items to NPC stores."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, npc_id: str, item_id: str, quantity: int = 1) -> GameStateData:
        """
        Execute item sale to NPC.

        Args:
            game_state: Current game state
            npc_id: ID of the NPC to sell to
            item_id: ID of the item to sell
            quantity: Quantity to sell

        Returns:
            Updated game state
        """
        if not game_state.player:
            raise ValueError("No player in game state")

        if npc_id not in game_state.npcs:
            raise ValueError(f"NPC '{npc_id}' not found in game state")

        npc = game_state.npcs[npc_id]

        if not npc.has_store_behavior():
            raise ValueError(f"NPC '{npc.name}' is not a merchant")

        if not game_state.player.has_item(item_id, quantity):
            raise ValueError(f"Player doesn't have enough '{item_id}' to sell")

        # Get item sell price (typically half of buy price)
        try:
            from src.game_data.items import ITEMS
            if item_id not in ITEMS:
                raise ValueError(f"Item '{item_id}' not found in item definitions")

            item_def = ITEMS[item_id]
            sell_price = max(1, item_def.value // 2)  # Sell for half price, minimum 1 gold
            total_value = sell_price * quantity

            # Execute transaction
            updated_player = game_state.player.remove_item_from_inventory(item_id, quantity)
            updated_player = updated_player.add_item_to_inventory("gold_coin", total_value)

            updated_npc = npc.add_item_to_stock(item_id, quantity)

            # Update game state
            new_npcs = game_state.npcs.copy()
            new_npcs[npc_id] = updated_npc

            return GameStateData(
                player=updated_player,
                monsters=game_state.monsters,
                items=game_state.items,
                npcs=new_npcs,
                current_level_id=game_state.current_level_id,
                collision_map=game_state.collision_map,
                level_tiles=game_state.level_tiles
            )

        except ImportError:
            raise ValueError("Item definitions not available")


class UpdateWanderingEntitiesUseCase:
    """Use case for updating wandering entities (NPCs and monsters)."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus
        self.monster_attack_use_case = MonsterAttackUseCase(event_bus)

    def execute(self, game_state: GameStateData, dt: float, config: Optional[Dict[str, Any]] = None, attacked_monsters: Optional[List[str]] = None) -> GameStateData:
        """
        Update all wandering entities in the game state.

        Args:
            game_state: Current game state
            dt: Delta time in seconds
            config: Optional configuration dictionary
            attacked_monsters: List of monster IDs that were attacked this frame

        Returns:
            Updated game state with new entity positions
        """
        # Get player position for AI behavior
        player_position = game_state.player.position if game_state.player else None

        # Update wandering entities using the core game logic
        updated_npcs, updated_monsters = update_wandering_entities(
            game_state.npcs,
            game_state.monsters,
            dt,
            game_state.collision_map,
            config,
            player_position,
            attacked_monsters
        )

        # Create intermediate game state with updated entities
        intermediate_game_state = GameStateData(
            player=game_state.player,
            monsters=updated_monsters,
            items=game_state.items,
            npcs=updated_npcs,
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles,
            tile_states=game_state.tile_states,
            quest_data=game_state.quest_data.copy() if hasattr(game_state, 'quest_data') and game_state.quest_data else {}
        )

        # Check for monsters ready to attack
        attacking_monsters = get_monsters_ready_to_attack(updated_monsters, player_position)

        # Execute monster attacks if any
        if attacking_monsters:
            intermediate_game_state = self.monster_attack_use_case.execute(
                intermediate_game_state, attacking_monsters
            )

        return intermediate_game_state


class SpawnLootUseCase:
    """Use case for spawning loot items when monsters are defeated."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, monster_id: str, position: Position) -> GameStateData:
        """
        Spawn loot items at the given position based on the defeated monster's loot table.

        Args:
            game_state: Current game state
            monster_id: ID of the defeated monster
            position: Position where the monster was defeated

        Returns:
            Updated game state with spawned loot items
        """
        try:
            from src.game_data.monsters import generate_loot
            from src.game_data.items import get_item_definition
            import uuid

            # Generate loot from monster's loot table
            loot = generate_loot(monster_id)

            if not loot:
                return game_state

            # Create item entities for each loot item
            new_items = game_state.items.copy()

            for item_id, quantity in loot.items():
                item_def = get_item_definition(item_id)
                if not item_def:
                    continue

                # Create item entity
                item_entity = Item(
                    id=str(uuid.uuid4()),
                    name=item_def.name,
                    position=position,
                    asset_id=item_def.asset_id,
                    item_type=item_def.item_type.value,
                    stackable=item_def.stackable,
                    stack_size=quantity,
                    properties=item_def.properties.copy()
                )

                new_items[item_entity.id] = item_entity

            # Return updated game state
            return GameStateData(
                player=game_state.player,
                monsters=game_state.monsters,
                items=new_items,
                npcs=game_state.npcs,
                current_level_id=game_state.current_level_id,
                collision_map=game_state.collision_map,
                level_tiles=game_state.level_tiles,
                tile_states=game_state.tile_states.copy(),
                quest_data=game_state.quest_data.copy() if hasattr(game_state, 'quest_data') and game_state.quest_data else {}
            )

        except ImportError:
            # If loot system not available, return unchanged state
            return game_state


class PickupItemUseCase:
    """Use case for picking up items from the world."""

    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus

    def execute(self, game_state: GameStateData, item_entity_id: str) -> GameStateData:
        """
        Pick up an item from the world and add it to the player's inventory.

        Args:
            game_state: Current game state
            item_entity_id: ID of the item entity to pick up

        Returns:
            Updated game state with item picked up
        """
        if not game_state.player:
            return game_state

        # Check if item exists in the world
        if item_entity_id not in game_state.items:
            return game_state

        item_entity = game_state.items[item_entity_id]

        try:
            from src.game_data.items import get_item_definition

            # Extract item_id from asset_id (e.g., "item.treasure.coin.gold" -> "gold_coin")
            item_id = None
            asset_parts = item_entity.asset_id.split('.')
            if len(asset_parts) >= 3:
                # Try different combinations to find the item
                potential_item_id = '_'.join(asset_parts[2:])  # Join remaining parts
                item_def = get_item_definition(potential_item_id)
                if item_def:
                    item_id = potential_item_id
                else:
                    # Try just the last part
                    potential_item_id = asset_parts[-1]
                    item_def = get_item_definition(potential_item_id)
                    if item_def:
                        item_id = potential_item_id
                    else:
                        # Try common patterns like "coin.gold" -> "gold_coin"
                        if len(asset_parts) >= 4:
                            potential_item_id = f"{asset_parts[-1]}_{asset_parts[-2]}"
                            item_def = get_item_definition(potential_item_id)
                            if item_def:
                                item_id = potential_item_id

            if not item_id:
                # Fallback: try to extract item ID from the entity's properties or name
                # For now, just skip unknown items
                return game_state

            # Check if player can carry this item
            can_add_use_case = CanAddItemToInventoryUseCase(self.event_bus)
            if not can_add_use_case.execute(game_state, item_id, item_entity.stack_size):
                # Inventory full - can't pick up
                return game_state

            # Add item to player inventory
            updated_player = game_state.player.add_item_to_inventory(item_id, item_entity.stack_size)

            # Remove item from world
            new_items = game_state.items.copy()
            del new_items[item_entity_id]

            # Return updated game state
            return GameStateData(
                player=updated_player,
                monsters=game_state.monsters,
                items=new_items,
                npcs=game_state.npcs,
                current_level_id=game_state.current_level_id,
                collision_map=game_state.collision_map,
                level_tiles=game_state.level_tiles,
                tile_states=game_state.tile_states
            )

        except (ImportError, ValueError):
            # If item system not available or error, return unchanged state
            return game_state
