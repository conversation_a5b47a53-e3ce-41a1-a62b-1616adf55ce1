"""
Dialog State Manager

This module manages dialog state for NPCs to support sequential conversations.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class DialogState:
    """State information for NPC dialog."""
    dialog_index: int = 0
    custom_data: Dict[str, Any] = field(default_factory=dict)


class DialogManager:
    """Manages dialog state for NPCs across the game."""
    
    def __init__(self):
        """Initialize the dialog manager."""
        self._npc_dialog_states: Dict[str, DialogState] = {}
    
    def get_dialog_state(self, npc_id: str) -> DialogState:
        """
        Get the dialog state for an NPC.
        
        Args:
            npc_id: ID of the NPC
            
        Returns:
            DialogState for the NPC
        """
        if npc_id not in self._npc_dialog_states:
            self._npc_dialog_states[npc_id] = DialogState()
        
        return self._npc_dialog_states[npc_id]
    
    def set_dialog_index(self, npc_id: str, index: int) -> None:
        """
        Set the dialog index for an NPC.
        
        Args:
            npc_id: ID of the NPC
            index: New dialog index
        """
        state = self.get_dialog_state(npc_id)
        state.dialog_index = index
    
    def get_dialog_index(self, npc_id: str) -> int:
        """
        Get the current dialog index for an NPC.
        
        Args:
            npc_id: ID of the NPC
            
        Returns:
            Current dialog index
        """
        return self.get_dialog_state(npc_id).dialog_index
    
    def reset_dialog(self, npc_id: str) -> None:
        """
        Reset dialog state for an NPC.
        
        Args:
            npc_id: ID of the NPC
        """
        if npc_id in self._npc_dialog_states:
            self._npc_dialog_states[npc_id].dialog_index = 0
    
    def set_custom_data(self, npc_id: str, key: str, value: Any) -> None:
        """
        Set custom data for an NPC's dialog state.
        
        Args:
            npc_id: ID of the NPC
            key: Data key
            value: Data value
        """
        state = self.get_dialog_state(npc_id)
        state.custom_data[key] = value
    
    def get_custom_data(self, npc_id: str, key: str, default: Any = None) -> Any:
        """
        Get custom data from an NPC's dialog state.
        
        Args:
            npc_id: ID of the NPC
            key: Data key
            default: Default value if key not found
            
        Returns:
            Custom data value or default
        """
        state = self.get_dialog_state(npc_id)
        return state.custom_data.get(key, default)
    
    def clear_all_states(self) -> None:
        """Clear all dialog states."""
        self._npc_dialog_states.clear()
    
    def get_state_data(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all dialog state data for saving.
        
        Returns:
            Dictionary of NPC dialog states
        """
        return {
            npc_id: {
                "dialog_index": state.dialog_index,
                "custom_data": state.custom_data.copy()
            }
            for npc_id, state in self._npc_dialog_states.items()
        }
    
    def load_state_data(self, state_data: Dict[str, Dict[str, Any]]) -> None:
        """
        Load dialog state data from save.
        
        Args:
            state_data: Dictionary of NPC dialog states
        """
        self._npc_dialog_states.clear()
        
        for npc_id, data in state_data.items():
            state = DialogState(
                dialog_index=data.get("dialog_index", 0),
                custom_data=data.get("custom_data", {}).copy()
            )
            self._npc_dialog_states[npc_id] = state


# Global dialog manager instance
_dialog_manager: Optional[DialogManager] = None


def get_dialog_manager() -> DialogManager:
    """Get the global dialog manager instance."""
    global _dialog_manager
    if _dialog_manager is None:
        _dialog_manager = DialogManager()
    return _dialog_manager


def initialize_dialog_manager() -> DialogManager:
    """Initialize a new dialog manager instance."""
    global _dialog_manager
    _dialog_manager = DialogManager()
    return _dialog_manager
