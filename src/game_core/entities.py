"""
Core Game Entities

This module contains the pure, abstract definitions for all game entities.
These are framework-agnostic data structures that define the game world.

NO PYGAME IMPORTS ALLOWED IN THIS MODULE.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from enum import Enum
import uuid


class Direction(Enum):
    """Cardinal and intercardinal directions for movement and targeting."""
    NORTH = "north"
    NORTHEAST = "northeast"
    EAST = "east"
    SOUTHEAST = "southeast"
    SOUTH = "south"
    SOUTHWEST = "southwest"
    WEST = "west"
    NORTHWEST = "northwest"

    def to_vector(self) -> tuple[float, float]:
        """Convert direction to a normalized vector (x, y)."""
        vectors = {
            Direction.NORTH: (0, -1),
            Direction.NORTHEAST: (0.707, -0.707),
            Direction.EAST: (1, 0),
            Direction.SOUTHEAST: (0.707, 0.707),
            Direction.SOUTH: (0, 1),
            Direction.SOUTHWEST: (-0.707, 0.707),
            Direction.WEST: (-1, 0),
            Direction.NORTHWEST: (-0.707, -0.707),
        }
        return vectors[self]


@dataclass(frozen=True)
class Position:
    """Immutable pixel-based position value object."""
    x: float
    y: float
    
    def move_pixels(self, dx: float, dy: float) -> "Position":
        """Return a new position moved by the given pixel offset."""
        return Position(self.x + dx, self.y + dy)
    
    def move(self, direction: Direction) -> "Position":
        """Return a new position moved one unit in the given direction (for tile-based movement)."""
        direction_map = {
            Direction.NORTH: (0, -1),
            Direction.NORTHEAST: (1, -1),
            Direction.EAST: (1, 0),
            Direction.SOUTHEAST: (1, 1),
            Direction.SOUTH: (0, 1),
            Direction.SOUTHWEST: (-1, 1),
            Direction.WEST: (-1, 0),
            Direction.NORTHWEST: (-1, -1),
        }
        dx, dy = direction_map[direction]
        return Position(self.x + dx, self.y + dy)

    def move_direction(self, direction: Direction, distance: float) -> "Position":
        """Return a new position moved in the given direction by distance pixels."""
        direction_map = {
            Direction.NORTH: (0, -1),
            Direction.NORTHEAST: (1, -1),
            Direction.EAST: (1, 0),
            Direction.SOUTHEAST: (1, 1),
            Direction.SOUTH: (0, 1),
            Direction.SOUTHWEST: (-1, 1),
            Direction.WEST: (-1, 0),
            Direction.NORTHWEST: (-1, -1),
        }
        dx, dy = direction_map[direction]
        # Normalize diagonal movement
        if dx != 0 and dy != 0:
            dx *= 0.707  # sqrt(2)/2 to maintain same speed diagonally
            dy *= 0.707
        return Position(self.x + dx * distance, self.y + dy * distance)
    
    def to_tile_coords(self, tile_size: int) -> tuple[int, int]:
        """Convert pixel position to tile coordinates."""
        return (int(self.x // tile_size), int(self.y // tile_size))
    
    @classmethod
    def from_tile_coords(cls, tile_x: int, tile_y: int, tile_size: int) -> "Position":
        """Create a position from tile coordinates (centered in tile)."""
        return cls(
            tile_x * tile_size + tile_size // 2,
            tile_y * tile_size + tile_size // 2
        )


@dataclass(frozen=True)
class Vector2:
    """Immutable 2D vector for directions and physics."""
    x: float
    y: float
    
    def normalize(self) -> "Vector2":
        """Return a normalized version of this vector."""
        magnitude = (self.x ** 2 + self.y ** 2) ** 0.5
        if magnitude == 0:
            return Vector2(0, 0)
        return Vector2(self.x / magnitude, self.y / magnitude)
    
    def magnitude(self) -> float:
        """Return the magnitude of this vector."""
        return (self.x ** 2 + self.y ** 2) ** 0.5


@dataclass(frozen=True)
class Stats:
    """Immutable character statistics."""
    hp: int
    max_hp: int
    mp: int
    max_mp: int
    strength: int
    defense: int
    speed: int
    
    def take_damage(self, damage: int) -> "Stats":
        """Return new stats with damage applied."""
        new_hp = max(0, self.hp - damage)
        return Stats(
            hp=new_hp,
            max_hp=self.max_hp,
            mp=self.mp,
            max_mp=self.max_mp,
            strength=self.strength,
            defense=self.defense,
            speed=self.speed
        )
    
    def heal(self, amount: int) -> "Stats":
        """Return new stats with healing applied."""
        new_hp = min(self.max_hp, self.hp + amount)
        return Stats(
            hp=new_hp,
            max_hp=self.max_hp,
            mp=self.mp,
            max_mp=self.max_mp,
            strength=self.strength,
            defense=self.defense,
            speed=self.speed
        )





@dataclass
class Player:
    """Player character entity with equipment and inventory systems."""
    id: str
    name: str
    position: Position
    asset_id: str
    stats: Stats
    size: tuple[int, int]  # width, height in pixels
    level: int = 1
    experience: int = 0

    # Equipment slots
    head_equipment: Optional[str] = None
    chest_equipment: Optional[str] = None
    legs_equipment: Optional[str] = None
    boots_equipment: Optional[str] = None
    main_hand_weapon: Optional[str] = None
    off_hand_equipment: Optional[str] = None  # shield or two-handed weapon

    # Inventory system
    inventory: Dict[str, int] = field(default_factory=dict)  # item_id -> quantity mapping
    inventory_max_size: int = 10  # maximum number of unique item types

    def __post_init__(self) -> None:
        """Ensure entity has a unique ID if none provided."""
        if not self.id:
            self.id = str(uuid.uuid4())

    def add_item_to_inventory(self, item_id: str, quantity: int = 1) -> "Player":
        """Return new player with item added to inventory."""
        new_inventory = self.inventory.copy()

        # Check if we have space for a new item type
        if item_id not in new_inventory and len(new_inventory) >= self.inventory_max_size:
            raise ValueError(f"Inventory is full (max {self.inventory_max_size} item types)")

        # Add the item
        new_inventory[item_id] = new_inventory.get(item_id, 0) + quantity

        return Player(
            id=self.id,
            name=self.name,
            position=self.position,
            asset_id=self.asset_id,
            stats=self.stats,
            size=self.size,
            level=self.level,
            experience=self.experience,
            head_equipment=self.head_equipment,
            chest_equipment=self.chest_equipment,
            legs_equipment=self.legs_equipment,
            boots_equipment=self.boots_equipment,
            main_hand_weapon=self.main_hand_weapon,
            off_hand_equipment=self.off_hand_equipment,
            inventory=new_inventory,
            inventory_max_size=self.inventory_max_size
        )

    def remove_item_from_inventory(self, item_id: str, quantity: int = 1) -> "Player":
        """Return new player with item removed from inventory."""
        new_inventory = self.inventory.copy()

        # Check if we have the item
        if item_id not in new_inventory:
            raise ValueError(f"Item '{item_id}' not found in inventory")

        current_quantity = new_inventory[item_id]
        if current_quantity < quantity:
            raise ValueError(f"Not enough '{item_id}' in inventory (have {current_quantity}, need {quantity})")

        # Remove the item
        new_quantity = current_quantity - quantity
        if new_quantity <= 0:
            del new_inventory[item_id]
        else:
            new_inventory[item_id] = new_quantity

        return Player(
            id=self.id,
            name=self.name,
            position=self.position,
            asset_id=self.asset_id,
            stats=self.stats,
            size=self.size,
            level=self.level,
            experience=self.experience,
            head_equipment=self.head_equipment,
            chest_equipment=self.chest_equipment,
            legs_equipment=self.legs_equipment,
            boots_equipment=self.boots_equipment,
            main_hand_weapon=self.main_hand_weapon,
            off_hand_equipment=self.off_hand_equipment,
            inventory=new_inventory,
            inventory_max_size=self.inventory_max_size
        )

    def has_item(self, item_id: str, quantity: int = 1) -> bool:
        """Check if player has specified item and quantity."""
        return self.inventory.get(item_id, 0) >= quantity

    def get_inventory_space_used(self) -> int:
        """Get number of unique item types in inventory."""
        return len(self.inventory)

    def get_inventory_space_remaining(self) -> int:
        """Get remaining inventory space for new item types."""
        return self.inventory_max_size - len(self.inventory)

    def level_up(self) -> "Player":
        """Return a new player with increased level and stats."""
        new_stats = Stats(
            hp=self.stats.max_hp + 10,
            max_hp=self.stats.max_hp + 10,
            mp=self.stats.max_mp + 5,
            max_mp=self.stats.max_mp + 5,
            strength=self.stats.strength + 2,
            defense=self.stats.defense + 1,
            speed=self.stats.speed + 1
        )
        return Player(
            id=self.id,
            name=self.name,
            position=self.position,
            asset_id=self.asset_id,
            stats=new_stats,
            size=self.size,
            level=self.level + 1,
            experience=self.experience,
            head_equipment=self.head_equipment,
            chest_equipment=self.chest_equipment,
            legs_equipment=self.legs_equipment,
            boots_equipment=self.boots_equipment,
            main_hand_weapon=self.main_hand_weapon,
            off_hand_equipment=self.off_hand_equipment,
            inventory=self.inventory,
            inventory_max_size=self.inventory_max_size
        )

    def equip_item(self, item_id: str, slot: str) -> "Player":
        """Return new player with item equipped in specified slot."""
        # Validate slot name
        valid_slots = {
            "head_equipment", "chest_equipment", "legs_equipment",
            "boots_equipment", "main_hand_weapon", "off_hand_equipment"
        }
        if slot not in valid_slots:
            raise ValueError(f"Invalid equipment slot: {slot}")

        # Check if item is in inventory
        if not self.has_item(item_id):
            raise ValueError(f"Item '{item_id}' not found in inventory")

        # Remove item from inventory
        new_player = self.remove_item_from_inventory(item_id, 1)

        # Get current equipment in slot (if any)
        current_equipment = getattr(new_player, slot)

        # If there's already equipment in the slot, add it back to inventory
        if current_equipment:
            new_player = new_player.add_item_to_inventory(current_equipment, 1)

        # Create new player with equipment
        equipment_kwargs = {
            "id": new_player.id,
            "name": new_player.name,
            "position": new_player.position,
            "asset_id": new_player.asset_id,
            "stats": new_player.stats,
            "size": new_player.size,
            "level": new_player.level,
            "experience": new_player.experience,
            "head_equipment": new_player.head_equipment,
            "chest_equipment": new_player.chest_equipment,
            "legs_equipment": new_player.legs_equipment,
            "boots_equipment": new_player.boots_equipment,
            "main_hand_weapon": new_player.main_hand_weapon,
            "off_hand_equipment": new_player.off_hand_equipment,
            "inventory": new_player.inventory,
            "inventory_max_size": new_player.inventory_max_size
        }
        equipment_kwargs[slot] = item_id

        return Player(**equipment_kwargs)

    def unequip_item(self, slot: str) -> "Player":
        """Return new player with item unequipped from specified slot."""
        # Validate slot name
        valid_slots = {
            "head_equipment", "chest_equipment", "legs_equipment",
            "boots_equipment", "main_hand_weapon", "off_hand_equipment"
        }
        if slot not in valid_slots:
            raise ValueError(f"Invalid equipment slot: {slot}")

        # Get current equipment in slot
        current_equipment = getattr(self, slot)
        if not current_equipment:
            raise ValueError(f"No equipment in slot '{slot}'")

        # Add item back to inventory
        new_player = self.add_item_to_inventory(current_equipment, 1)

        # Create new player without equipment
        equipment_kwargs = {
            "id": new_player.id,
            "name": new_player.name,
            "position": new_player.position,
            "asset_id": new_player.asset_id,
            "stats": new_player.stats,
            "size": new_player.size,
            "level": new_player.level,
            "experience": new_player.experience,
            "head_equipment": new_player.head_equipment,
            "chest_equipment": new_player.chest_equipment,
            "legs_equipment": new_player.legs_equipment,
            "boots_equipment": new_player.boots_equipment,
            "main_hand_weapon": new_player.main_hand_weapon,
            "off_hand_equipment": new_player.off_hand_equipment,
            "inventory": new_player.inventory,
            "inventory_max_size": new_player.inventory_max_size
        }
        equipment_kwargs[slot] = None

        return Player(**equipment_kwargs)

    def get_equipment_dict(self) -> Dict[str, Optional[str]]:
        """Get all equipment as a dictionary."""
        return {
            "head_equipment": self.head_equipment,
            "chest_equipment": self.chest_equipment,
            "legs_equipment": self.legs_equipment,
            "boots_equipment": self.boots_equipment,
            "main_hand_weapon": self.main_hand_weapon,
            "off_hand_equipment": self.off_hand_equipment
        }

    def get_equipped_item_in_slot(self, slot: str) -> Optional[str]:
        """Get the item ID equipped in a specific slot."""
        valid_slots = {
            "head_equipment", "chest_equipment", "legs_equipment",
            "boots_equipment", "main_hand_weapon", "off_hand_equipment"
        }
        if slot not in valid_slots:
            raise ValueError(f"Invalid equipment slot: {slot}")

        return getattr(self, slot)

    def get_total_stats(self) -> "Stats":
        """Get total stats including equipment bonuses."""
        # Start with base stats
        total_stats = Stats(
            hp=self.stats.hp,
            max_hp=self.stats.max_hp,
            mp=self.stats.mp,
            max_mp=self.stats.max_mp,
            strength=self.stats.strength,
            defense=self.stats.defense,
            speed=self.stats.speed
        )

        # Add equipment bonuses
        equipment_bonuses = self.get_equipment_bonuses()

        total_stats = Stats(
            hp=total_stats.hp,
            max_hp=total_stats.max_hp + equipment_bonuses.get("max_hp_bonus", 0),
            mp=total_stats.mp,
            max_mp=total_stats.max_mp + equipment_bonuses.get("max_mp_bonus", 0),
            strength=total_stats.strength + equipment_bonuses.get("strength_bonus", 0),
            defense=total_stats.defense + equipment_bonuses.get("defense_bonus", 0),
            speed=total_stats.speed + equipment_bonuses.get("speed_bonus", 0)
        )

        return total_stats

    def get_equipment_bonuses(self) -> Dict[str, int]:
        """Get all equipment bonuses as a dictionary."""
        bonuses = {
            "damage_bonus": 0,
            "defense_bonus": 0,
            "strength_bonus": 0,
            "speed_bonus": 0,
            "max_hp_bonus": 0,
            "max_mp_bonus": 0,
            "attack_speed_bonus": 0
        }

        # Get all equipped items
        equipped_items = [
            self.head_equipment,
            self.chest_equipment,
            self.legs_equipment,
            self.boots_equipment,
            self.main_hand_weapon,
            self.off_hand_equipment
        ]

        # Calculate bonuses from each equipped item
        try:
            from src.game_data.items import ITEMS

            for item_id in equipped_items:
                if item_id and item_id in ITEMS:
                    item_def = ITEMS[item_id]
                    item_props = item_def.properties

                    # Add damage bonus
                    if "damage_bonus" in item_props:
                        bonuses["damage_bonus"] += item_props["damage_bonus"]

                    # Add defense bonus
                    if "defense_bonus" in item_props:
                        bonuses["defense_bonus"] += item_props["defense_bonus"]

                    # Add stat bonuses
                    if "strength_bonus" in item_props:
                        bonuses["strength_bonus"] += item_props["strength_bonus"]

                    if "speed_bonus" in item_props:
                        bonuses["speed_bonus"] += item_props["speed_bonus"]

                    if "max_hp_bonus" in item_props:
                        bonuses["max_hp_bonus"] += item_props["max_hp_bonus"]

                    if "max_mp_bonus" in item_props:
                        bonuses["max_mp_bonus"] += item_props["max_mp_bonus"]

                    if "attack_speed_bonus" in item_props:
                        bonuses["attack_speed_bonus"] += item_props["attack_speed_bonus"]

        except ImportError:
            # Fallback if items module not available
            pass

        return bonuses


@dataclass
class Monster:
    """Monster entity."""
    id: str
    name: str
    position: Position
    asset_id: str
    stats: Stats
    monster_type: str
    ai_behavior: str = "aggressive"
    experience_reward: int = 10

    # Wander behavior (will be set after import to avoid circular dependency)
    wander_behavior: Optional[Any] = None  # WanderBehavior instance

    # AI behavior (will be set after import to avoid circular dependency)
    ai_behavior_instance: Optional[Any] = None  # AIBehavior instance

    def __post_init__(self) -> None:
        """Ensure entity has a unique ID if none provided."""
        if not self.id:
            self.id = str(uuid.uuid4())

    def is_alive(self) -> bool:
        """Check if the monster is still alive."""
        return self.stats.hp > 0


@dataclass
class Item:
    """Item entity."""
    id: str
    name: str
    position: Position
    asset_id: str
    item_type: str
    stackable: bool = False
    stack_size: int = 1
    properties: Optional[Dict[str, Any]] = None
    
    def __post_init__(self) -> None:
        """Ensure entity has a unique ID if none provided."""
        if not self.id:
            self.id = str(uuid.uuid4())
        
        if self.properties is None:
            self.properties = {}


@dataclass
class NPC:
    """Non-Player Character entity with behavior and interaction capabilities."""
    id: str
    name: str
    position: Position
    asset_id: str
    npc_type: str  # merchant, armourer, weaponsmith, innkeeper, commoner, guard
    behavior: str  # store, dialog
    dialog: list[str] = field(default_factory=list)  # Available dialog options (sequential)
    inventory: Dict[str, int] = field(default_factory=dict)  # For store NPCs: item_id -> quantity
    properties: Dict[str, Any] = field(default_factory=dict)  # Additional NPC-specific properties

    # Wander behavior (will be set after import to avoid circular dependency)
    wander_behavior: Optional[Any] = None  # WanderBehavior instance

    def __post_init__(self) -> None:
        """Ensure entity has a unique ID if none provided."""
        if not self.id:
            self.id = str(uuid.uuid4())

    def has_store_behavior(self) -> bool:
        """Check if this NPC opens a store interface."""
        return self.behavior == "store"

    def has_dialog_behavior(self) -> bool:
        """Check if this NPC shows dialog."""
        return self.behavior == "dialog"

    def get_dialog(self, dialog_index: int = 0) -> tuple[str, int]:
        """
        Get the next dialog line in sequence.

        Args:
            dialog_index: Current dialog index

        Returns:
            Tuple of (dialog_text, next_index)
        """
        if not self.dialog:
            return f"{self.name} has nothing to say.", 0

        # Ensure index is within bounds
        current_index = dialog_index % len(self.dialog)
        dialog_text = self.dialog[current_index]
        next_index = (current_index + 1) % len(self.dialog)

        return dialog_text, next_index

    def has_item_in_stock(self, item_id: str, quantity: int = 1) -> bool:
        """Check if NPC has specified item and quantity in stock."""
        return self.inventory.get(item_id, 0) >= quantity

    def add_item_to_stock(self, item_id: str, quantity: int = 1) -> "NPC":
        """Return new NPC with item added to stock."""
        new_inventory = self.inventory.copy()
        new_inventory[item_id] = new_inventory.get(item_id, 0) + quantity

        return NPC(
            id=self.id,
            name=self.name,
            position=self.position,
            asset_id=self.asset_id,
            npc_type=self.npc_type,
            behavior=self.behavior,
            dialog=self.dialog.copy(),
            inventory=new_inventory,
            properties=self.properties.copy()
        )

    def remove_item_from_stock(self, item_id: str, quantity: int = 1) -> "NPC":
        """Return new NPC with item removed from stock."""
        new_inventory = self.inventory.copy()

        if item_id not in new_inventory:
            raise ValueError(f"Item '{item_id}' not found in NPC stock")

        current_quantity = new_inventory[item_id]
        if current_quantity < quantity:
            raise ValueError(f"Not enough '{item_id}' in stock (have {current_quantity}, need {quantity})")

        new_quantity = current_quantity - quantity
        if new_quantity <= 0:
            del new_inventory[item_id]
        else:
            new_inventory[item_id] = new_quantity

        return NPC(
            id=self.id,
            name=self.name,
            position=self.position,
            asset_id=self.asset_id,
            npc_type=self.npc_type,
            behavior=self.behavior,
            dialog=self.dialog.copy(),
            inventory=new_inventory,
            properties=self.properties.copy()
        )


@dataclass
class Tile:
    """Map tile definition."""
    tile_type: str
    solid: bool
    asset_id: str
    movement_modifier: float = 1.0
    properties: Dict[str, Any] = None

    def __post_init__(self) -> None:
        if self.properties is None:
            self.properties = {}

    def can_interact(self) -> bool:
        """Check if this tile can be interacted with."""
        return self.properties.get('can_interact', False)

    def is_open(self) -> bool:
        """Check if this tile is in an open state (for doors, etc.)."""
        return self.properties.get('is_open', False)

    def get_current_asset_id(self) -> str:
        """Get the current asset ID based on tile state."""
        if self.can_interact():
            if self.is_open():
                return self.properties.get('open_asset_id', self.asset_id)
            else:
                return self.properties.get('closed_asset_id', self.asset_id)
        return self.asset_id

    def toggle_state(self) -> "Tile":
        """Toggle the tile state (open/closed) and return a new Tile instance."""
        if not self.can_interact():
            return self

        new_properties = self.properties.copy()
        current_open_state = new_properties.get('is_open', False)
        new_properties['is_open'] = not current_open_state

        # Update solid state based on open/closed
        new_solid = not new_properties['is_open']  # Open doors are not solid

        return Tile(
            tile_type=self.tile_type,
            solid=new_solid,
            asset_id=self.asset_id,  # Keep original asset_id, use get_current_asset_id() for rendering
            movement_modifier=self.movement_modifier,
            properties=new_properties
        )
