"""
Wander System for Entities

This module provides a generic wandering behavior system for NPCs, animals, and monsters.
It handles movement patterns, collision detection, and state management for wandering entities.
It also includes AI behavior systems for chase, flee, and combat behaviors.

NO PYGAME IMPORTS ALLOWED IN THIS MODULE.
"""

import random
import time
import math
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any
from .entities import Position, Direction, NPC, Monster


class WanderState(Enum):
    """States for wandering behavior."""
    IDLE = "idle"           # Standing still, deciding what to do next
    MOVING = "moving"       # Currently moving towards a target
    PAUSED = "paused"       # Temporarily stopped (e.g., reached destination)


class AIState(Enum):
    """States for AI behavior."""
    WANDERING = "wandering"     # Normal wandering behavior
    CHASING = "chasing"         # Chasing a target (aggressive monsters)
    FLEEING = "fleeing"         # Fleeing from a threat (passive monsters)
    ATTACKING = "attacking"     # In combat range, attacking
    RETURNING = "returning"     # Returning to spawn area after losing target


@dataclass
class AIBehavior:
    """
    Configuration and state for AI behavior including chase, flee, and combat.

    This class manages the AI state and behavior for monsters, including
    detection, chase, flee, and attack behaviors.
    """
    # Configuration
    enabled: bool = True                    # Whether AI behavior is enabled
    detection_radius: float = 6.0          # Detection radius in tiles
    chase_speed_multiplier: float = 1.5    # Speed multiplier when chasing
    flee_speed_multiplier: float = 2.0     # Speed multiplier when fleeing
    attack_range: float = 1.5              # Attack range in tiles
    flee_duration: float = 5.0             # How long to flee after being attacked
    return_to_spawn: bool = True           # Whether to return to spawn after losing target
    proximity_flee_radius: float = 4.0     # Radius to continue fleeing when player approaches
    min_safe_distance: float = 8.0         # Minimum distance to consider "safe" from player

    # State (changes during gameplay)
    current_state: AIState = AIState.WANDERING
    target_position: Optional[Position] = None
    last_known_player_position: Optional[Position] = None
    state_start_time: float = field(default_factory=time.time)
    flee_start_time: Optional[float] = None
    has_been_attacked: bool = False
    last_attack_time: float = 0.0
    attack_cooldown: float = 2.0  # Seconds between attacks

    # Stuck detection for flee behavior
    last_flee_tile: Optional[tuple] = None  # (tile_x, tile_y) of last position
    flee_stuck_time: float = 0.0
    flee_stuck_threshold: float = 0.25  # 250ms before considering stuck
    current_flee_direction: Optional[Direction] = None  # Track current flee direction

    def transition_to_state(self, new_state: AIState, target_pos: Optional[Position] = None) -> None:
        """Transition to a new AI state."""
        self.current_state = new_state
        self.target_position = target_pos
        self.state_start_time = time.time()

        if new_state == AIState.FLEEING:
            self.flee_start_time = time.time()
            # Reset stuck detection when starting to flee
            self.last_flee_tile = None
            self.flee_stuck_time = 0.0
            self.current_flee_direction = None

    def is_fleeing_expired(self) -> bool:
        """Check if flee duration has expired."""
        if self.flee_start_time is None:
            return True
        return time.time() - self.flee_start_time > self.flee_duration

    def update_stuck_detection(self, current_pos: Position, tile_size: int = 128) -> bool:
        """
        Update stuck detection for flee behavior based on tile position.

        Args:
            current_pos: Current position of the entity
            tile_size: Size of tiles in pixels

        Returns:
            True if entity is considered stuck (same tile for > 250ms), False otherwise
        """
        current_time = time.time()
        current_tile = (int(current_pos.x // tile_size), int(current_pos.y // tile_size))

        if self.last_flee_tile is None:
            # First update, just record tile position
            self.last_flee_tile = current_tile
            self.flee_stuck_time = current_time
            return False

        if current_tile == self.last_flee_tile:
            # Still on the same tile, check if we've been stuck long enough
            if current_time - self.flee_stuck_time >= self.flee_stuck_threshold:
                # Reset the timer so we don't immediately get stuck again
                self.flee_stuck_time = current_time
                return True
        else:
            # Moved to a different tile, reset stuck timer
            self.flee_stuck_time = current_time
            self.last_flee_tile = current_tile

        return False


@dataclass
class WanderBehavior:
    """
    Wandering behavior configuration and state for an entity.
    
    This class encapsulates all the data needed to make an entity wander around
    in a realistic and configurable way.
    """
    # Configuration (set once, rarely changed)
    enabled: bool = True                    # Whether this entity should wander
    wander_radius: float = 3.0             # Maximum distance from spawn point (in tiles)
    move_speed: float = 1.0                # Movement speed multiplier (1.0 = normal speed)
    idle_time_min: float = 2.0             # Minimum time to stay idle (seconds)
    idle_time_max: float = 8.0             # Maximum time to stay idle (seconds)
    move_time_min: float = 1.0             # Minimum time to move in one direction (seconds)
    move_time_max: float = 4.0             # Maximum time to move in one direction (seconds)
    
    # State (changes during gameplay)
    current_state: WanderState = WanderState.IDLE
    spawn_position: Optional[Position] = None      # Original spawn position (center of wander area)
    target_position: Optional[Position] = None     # Current movement target
    state_start_time: float = 0.0                  # When current state started
    state_duration: float = 0.0                    # How long to stay in current state
    current_direction: Optional[Direction] = None  # Current movement direction
    
    def __post_init__(self):
        """Initialize state timing."""
        if self.state_start_time == 0.0:
            self.state_start_time = time.time()
            self._set_random_idle_duration()
    
    def _set_random_idle_duration(self) -> None:
        """Set a random duration for idle state."""
        self.state_duration = random.uniform(self.idle_time_min, self.idle_time_max)
    
    def _set_random_move_duration(self) -> None:
        """Set a random duration for moving state."""
        self.state_duration = random.uniform(self.move_time_min, self.move_time_max)
    
    def is_state_expired(self) -> bool:
        """Check if the current state has been active long enough."""
        return time.time() - self.state_start_time >= self.state_duration
    
    def transition_to_idle(self) -> None:
        """Transition to idle state."""
        self.current_state = WanderState.IDLE
        self.state_start_time = time.time()
        self.target_position = None
        self.current_direction = None
        self._set_random_idle_duration()
    
    def transition_to_moving(self, target_pos: Position, direction: Direction) -> None:
        """Transition to moving state."""
        self.current_state = WanderState.MOVING
        self.state_start_time = time.time()
        self.target_position = target_pos
        self.current_direction = direction
        self._set_random_move_duration()
    
    def transition_to_paused(self, pause_duration: float = 1.0) -> None:
        """Transition to paused state for a specific duration."""
        self.current_state = WanderState.PAUSED
        self.state_start_time = time.time()
        self.state_duration = pause_duration
        self.current_direction = None


def _get_random_direction() -> Direction:
    """Get a random movement direction."""
    directions = [
        Direction.NORTH, Direction.NORTHEAST, Direction.EAST, Direction.SOUTHEAST,
        Direction.SOUTH, Direction.SOUTHWEST, Direction.WEST, Direction.NORTHWEST
    ]
    return random.choice(directions)


def get_random_escape_direction(
    current_pos: Position,
    threat_pos: Position,
    collision_map: List[List[bool]]
) -> Direction:
    """
    Get a random direction for escaping when stuck, preferring directions away from threat.

    Args:
        current_pos: Current position of the fleeing entity
        threat_pos: Position of the threat (player)
        collision_map: Collision map for checking passable directions

    Returns:
        A random direction that's passable and preferably away from threat
    """
    # Get all possible directions
    all_directions = [
        Direction.NORTH, Direction.NORTHEAST, Direction.EAST, Direction.SOUTHEAST,
        Direction.SOUTH, Direction.SOUTHWEST, Direction.WEST, Direction.NORTHWEST
    ]

    # Filter to only passable directions
    passable_directions = [
        direction for direction in all_directions
        if is_direction_passable(current_pos, direction, collision_map)
    ]

    if not passable_directions:
        # If no directions are passable, return a random one anyway
        return random.choice(all_directions)

    # Calculate which directions are generally away from the threat
    dx = current_pos.x - threat_pos.x
    dy = current_pos.y - threat_pos.y

    # Directions that move away from threat (prefer these)
    away_directions = []
    # Directions that move toward threat (avoid these)
    toward_directions = []
    # Neutral directions (perpendicular to threat)
    neutral_directions = []

    for direction in passable_directions:
        direction_map = {
            Direction.NORTH: (0, -1),
            Direction.NORTHEAST: (1, -1),
            Direction.EAST: (1, 0),
            Direction.SOUTHEAST: (1, 1),
            Direction.SOUTH: (0, 1),
            Direction.SOUTHWEST: (-1, 1),
            Direction.WEST: (-1, 0),
            Direction.NORTHWEST: (-1, -1),
        }

        dir_dx, dir_dy = direction_map[direction]

        # Calculate dot product to determine if direction is away from threat
        dot_product = dx * dir_dx + dy * dir_dy

        if dot_product > 0.3:  # Moving away from threat
            away_directions.append(direction)
        elif dot_product < -0.3:  # Moving toward threat
            toward_directions.append(direction)
        else:  # Neutral/perpendicular
            neutral_directions.append(direction)

    # Choose direction with preference: away > neutral > toward
    if away_directions:
        return random.choice(away_directions)
    elif neutral_directions:
        return random.choice(neutral_directions)
    else:
        return random.choice(passable_directions)


def _calculate_distance(pos1: Position, pos2: Position, tile_size: int = 128) -> float:
    """Calculate distance between two positions in tiles."""
    # Convert pixel positions to tile positions for distance calculation
    tile1_x, tile1_y = pos1.to_tile_coords(tile_size)
    tile2_x, tile2_y = pos2.to_tile_coords(tile_size)
    dx = tile1_x - tile2_x
    dy = tile1_y - tile2_y
    return (dx * dx + dy * dy) ** 0.5


def _is_within_wander_radius(current_pos: Position, spawn_pos: Position, radius: float, tile_size: int = 128) -> bool:
    """Check if a position is within the allowed wander radius."""
    distance = _calculate_distance(current_pos, spawn_pos, tile_size)
    return distance <= radius


def _find_valid_wander_target(
    current_pos: Position,
    spawn_pos: Position,
    wander_radius: float,
    collision_map: List[List[bool]],
    tile_size: int = 128
) -> Optional[Tuple[Position, Direction]]:
    """
    Find a valid position to wander to within the wander radius.
    
    Returns:
        Tuple of (target_position, direction) or None if no valid target found
    """
    # Try multiple random directions to find a valid move
    attempts = 8  # Try up to 8 different directions
    
    for _ in range(attempts):
        direction = _get_random_direction()
        
        # Calculate potential target position (1-3 tiles away in chosen direction)
        distance_tiles = random.uniform(1.0, 3.0)
        distance_pixels = distance_tiles * tile_size
        target_pos = current_pos.move_direction(direction, distance_pixels)

        # Check if target is within wander radius
        if not _is_within_wander_radius(target_pos, spawn_pos, wander_radius, tile_size):
            continue

        # Check if target position is valid (not blocked)
        target_tile_x, target_tile_y = target_pos.to_tile_coords(tile_size)
        
        # Bounds check
        if (target_tile_y < 0 or target_tile_y >= len(collision_map) or
            target_tile_x < 0 or target_tile_x >= len(collision_map[0])):
            continue
        
        # Collision check
        if collision_map[target_tile_y][target_tile_x]:
            continue
        
        # Found a valid target
        return target_pos, direction
    
    return None


def _update_wander_behavior(
    entity: Any,  # NPC or Monster
    wander: WanderBehavior,
    dt: float,
    collision_map: List[List[bool]],
    base_move_speed: float = 64.0  # pixels per second
) -> Position:
    """
    Update an entity's wander behavior and return new position.
    
    Args:
        entity: The entity (NPC or Monster) that is wandering
        wander: The wander behavior configuration and state
        dt: Delta time in seconds
        collision_map: 2D collision map for the level
        base_move_speed: Base movement speed in pixels per second
        
    Returns:
        New position for the entity
    """
    current_pos = entity.position
    
    # Initialize spawn position if not set
    if wander.spawn_position is None:
        wander.spawn_position = current_pos
    
    # Handle state transitions
    if wander.current_state == WanderState.IDLE:
        if wander.is_state_expired():
            # Try to find a place to wander to
            tile_size = 128  # Default tile size, could be passed as parameter
            target_info = _find_valid_wander_target(
                current_pos, wander.spawn_position, wander.wander_radius, collision_map, tile_size
            )
            
            if target_info:
                target_pos, direction = target_info
                wander.transition_to_moving(target_pos, direction)
            else:
                # Couldn't find a valid target, stay idle a bit longer
                wander.transition_to_idle()
        
        # Stay at current position while idle
        return current_pos
    
    elif wander.current_state == WanderState.MOVING:
        if wander.target_position is None or wander.current_direction is None:
            # Invalid state, go back to idle
            wander.transition_to_idle()
            return current_pos
        
        # Move towards target
        move_distance = base_move_speed * wander.move_speed * dt
        new_pos = current_pos.move_direction(wander.current_direction, move_distance)
        
        # Check if we've reached the target or hit an obstacle
        tile_size = 128  # Default tile size, could be passed as parameter
        distance_to_target = _calculate_distance(new_pos, wander.target_position, tile_size)

        # Check for collision at new position
        new_tile_x, new_tile_y = new_pos.to_tile_coords(tile_size)
        
        collision = False
        if (new_tile_y < 0 or new_tile_y >= len(collision_map) or
            new_tile_x < 0 or new_tile_x >= len(collision_map[0]) or
            collision_map[new_tile_y][new_tile_x]):
            collision = True
        
        # If we hit an obstacle or reached target, transition to paused then idle
        if collision or distance_to_target < 0.5 or wander.is_state_expired():
            wander.transition_to_paused(random.uniform(0.5, 2.0))
            return current_pos  # Don't move if we hit an obstacle
        
        return new_pos
    
    elif wander.current_state == WanderState.PAUSED:
        if wander.is_state_expired():
            wander.transition_to_idle()
        return current_pos
    
    # Default: return current position
    return current_pos


def update_wandering_entities(
    npcs: Dict[str, NPC],
    monsters: Dict[str, Monster],
    dt: float,
    collision_map: List[List[bool]],
    config: Optional[Dict[str, Any]] = None,
    player_position: Optional[Position] = None,
    attacked_monsters: Optional[List[str]] = None
) -> Tuple[Dict[str, NPC], Dict[str, Monster]]:
    """
    Update all wandering entities and return updated collections.

    Args:
        npcs: Dictionary of NPCs by ID
        monsters: Dictionary of monsters by ID
        dt: Delta time in seconds
        collision_map: 2D collision map for collision detection
        config: Optional configuration dictionary with movement settings
        player_position: Current player position for AI behavior
        attacked_monsters: List of monster IDs that were attacked this frame

    Returns:
        Tuple of (updated_npcs, updated_monsters)
    """
    # Get base movement speed from config
    base_speed = 64.0  # Default pixels per second
    if config:
        try:
            # Convert tiles per second to pixels per second
            tile_size = getattr(config.rendering, 'tile_size', 128) if hasattr(config, 'rendering') else 128
            tiles_per_sec = getattr(config.movement, 'player_move_speed', 6.67) if hasattr(config, 'movement') else 6.67
            base_speed = tiles_per_sec * tile_size * 0.5  # NPCs move slower than player
        except (AttributeError, TypeError):
            # Fallback if config structure is different
            base_speed = 64.0
    
    updated_npcs = {}
    updated_monsters = {}

    # Update wandering NPCs
    for npc_id, npc in npcs.items():
        if hasattr(npc, 'wander_behavior') and npc.wander_behavior and npc.wander_behavior.enabled:
            new_position = _update_wander_behavior(
                npc, npc.wander_behavior, dt, collision_map, base_speed
            )
            
            # Create updated NPC with new position
            updated_npcs[npc_id] = NPC(
                id=npc.id,
                name=npc.name,
                position=new_position,
                asset_id=npc.asset_id,
                npc_type=npc.npc_type,
                behavior=npc.behavior,
                dialog=npc.dialog,
                inventory=npc.inventory,
                properties=npc.properties,
                use_sequential_dialog=npc.use_sequential_dialog
            )
            # Preserve wander behavior
            updated_npcs[npc_id].wander_behavior = npc.wander_behavior
        else:
            # NPC doesn't wander, keep as-is
            updated_npcs[npc_id] = npc
    
    # Find passive monsters that should flee due to nearby attacks
    fleeing_monsters = set()
    if attacked_monsters and player_position:
        for attacked_id in attacked_monsters:
            if attacked_id in monsters:
                attacked_monster = monsters[attacked_id]
                # Find all passive monsters within 6 tiles of the attacked monster
                for monster_id, monster in monsters.items():
                    if (monster.ai_behavior == "peaceful" and
                        monster_id != attacked_id and
                        calculate_distance(monster.position, attacked_monster.position) <= 6.0):
                        fleeing_monsters.add(monster_id)

    # Update monsters with AI behavior
    for monster_id, monster in monsters.items():
        new_position = monster.position

        # Check if monster has AI behavior
        if hasattr(monster, 'ai_behavior_instance') and monster.ai_behavior_instance:
            # Update AI behavior
            should_flee = monster_id in fleeing_monsters or (attacked_monsters and monster_id in attacked_monsters)
            if should_flee and monster.ai_behavior in ["peaceful", "skittish"]:
                # Make passive and skittish monsters flee
                if not monster.ai_behavior_instance.current_state == AIState.FLEEING:
                    monster.ai_behavior_instance.transition_to_state(AIState.FLEEING, player_position)

            new_position = _update_ai_behavior(
                monster, monster.ai_behavior_instance, player_position, dt, collision_map, base_speed, attacked_monsters
            )
        elif hasattr(monster, 'wander_behavior') and monster.wander_behavior and monster.wander_behavior.enabled:
            # Fall back to wander behavior
            new_position = _update_wander_behavior(
                monster, monster.wander_behavior, dt, collision_map, base_speed
            )

        # Create updated monster with new position
        updated_monsters[monster_id] = Monster(
            id=monster.id,
            name=monster.name,
            position=new_position,
            asset_id=monster.asset_id,
            stats=monster.stats,
            monster_type=monster.monster_type,
            ai_behavior=monster.ai_behavior,
            experience_reward=monster.experience_reward
        )

        # Preserve behaviors
        if hasattr(monster, 'wander_behavior'):
            updated_monsters[monster_id].wander_behavior = monster.wander_behavior
        if hasattr(monster, 'ai_behavior_instance'):
            updated_monsters[monster_id].ai_behavior_instance = monster.ai_behavior_instance
    
    return updated_npcs, updated_monsters


def calculate_distance(pos1: Position, pos2: Position) -> float:
    """Calculate distance between two positions in tiles."""
    dx = pos1.x - pos2.x
    dy = pos1.y - pos2.y
    tile_size = 128  # Default tile size
    return math.sqrt(dx * dx + dy * dy) / tile_size


def calculate_direction_to_target(from_pos: Position, to_pos: Position) -> Direction:
    """Calculate the direction to move from one position to another."""
    dx = to_pos.x - from_pos.x
    dy = to_pos.y - from_pos.y

    # Determine primary direction based on larger component
    if abs(dx) > abs(dy):
        return Direction.EAST if dx > 0 else Direction.WEST
    else:
        return Direction.SOUTH if dy > 0 else Direction.NORTH


def find_flee_direction(from_pos: Position, threat_pos: Position) -> Direction:
    """Find the best direction to flee from a threat with some randomness."""
    dx = from_pos.x - threat_pos.x
    dy = from_pos.y - threat_pos.y

    # Add some randomness to the flee direction
    # 70% chance to use optimal direction, 30% chance for adjacent directions
    if random.random() < 0.7:
        # Move away from the threat (optimal direction)
        if abs(dx) > abs(dy):
            primary_direction = Direction.EAST if dx > 0 else Direction.WEST
        else:
            primary_direction = Direction.SOUTH if dy > 0 else Direction.NORTH
    else:
        # Choose a somewhat random direction that's still generally away from threat
        if abs(dx) > abs(dy):
            # Moving horizontally, add some vertical randomness
            if random.random() < 0.5:
                primary_direction = Direction.NORTHEAST if dx > 0 else Direction.NORTHWEST
            else:
                primary_direction = Direction.SOUTHEAST if dx > 0 else Direction.SOUTHWEST
        else:
            # Moving vertically, add some horizontal randomness
            if random.random() < 0.5:
                primary_direction = Direction.NORTHEAST if dy > 0 else Direction.SOUTHEAST
            else:
                primary_direction = Direction.NORTHWEST if dy > 0 else Direction.SOUTHWEST

    return primary_direction


def get_alternative_directions(primary_direction: Direction) -> List[Direction]:
    """Get alternative directions to try when primary direction is blocked."""
    direction_alternatives = {
        Direction.NORTH: [Direction.NORTHEAST, Direction.NORTHWEST, Direction.EAST, Direction.WEST, Direction.SOUTH],
        Direction.SOUTH: [Direction.SOUTHEAST, Direction.SOUTHWEST, Direction.EAST, Direction.WEST, Direction.NORTH],
        Direction.EAST: [Direction.NORTHEAST, Direction.SOUTHEAST, Direction.NORTH, Direction.SOUTH, Direction.WEST],
        Direction.WEST: [Direction.NORTHWEST, Direction.SOUTHWEST, Direction.NORTH, Direction.SOUTH, Direction.EAST],
        Direction.NORTHEAST: [Direction.NORTH, Direction.EAST, Direction.NORTHWEST, Direction.SOUTHEAST, Direction.SOUTH],
        Direction.NORTHWEST: [Direction.NORTH, Direction.WEST, Direction.NORTHEAST, Direction.SOUTHWEST, Direction.SOUTH],
        Direction.SOUTHEAST: [Direction.SOUTH, Direction.EAST, Direction.SOUTHWEST, Direction.NORTHEAST, Direction.NORTH],
        Direction.SOUTHWEST: [Direction.SOUTH, Direction.WEST, Direction.SOUTHEAST, Direction.NORTHWEST, Direction.NORTH],
    }
    return direction_alternatives.get(primary_direction, [])


def is_direction_passable(
    current_pos: Position,
    direction: Direction,
    collision_map: List[List[bool]]
) -> bool:
    """Check if movement in a specific direction is possible."""
    # Convert pixel positions to tile positions for collision checking
    tile_size = 128  # Default tile size

    # Calculate movement vector for the direction
    direction_map = {
        Direction.NORTH: (0, -1),
        Direction.NORTHEAST: (1, -1),
        Direction.EAST: (1, 0),
        Direction.SOUTHEAST: (1, 1),
        Direction.SOUTH: (0, 1),
        Direction.SOUTHWEST: (-1, 1),
        Direction.WEST: (-1, 0),
        Direction.NORTHWEST: (-1, -1),
    }

    dx, dy = direction_map.get(direction, (0, 0))

    # Calculate new tile position
    current_tile_x = int(current_pos.x // tile_size)
    current_tile_y = int(current_pos.y // tile_size)
    new_tile_x = current_tile_x + dx
    new_tile_y = current_tile_y + dy

    # Check bounds
    if (new_tile_y < 0 or new_tile_y >= len(collision_map) or
        new_tile_x < 0 or new_tile_x >= len(collision_map[0])):
        return False

    # Check collision
    return not collision_map[new_tile_y][new_tile_x]


def find_best_direction(
    current_pos: Position,
    target_pos: Position,
    collision_map: List[List[bool]],
    is_fleeing: bool = False
) -> Direction:
    """Find the best direction to move toward (or away from) a target, considering obstacles."""
    if is_fleeing:
        # For fleeing, we want to move away from the target
        primary_direction = find_flee_direction(current_pos, target_pos)
    else:
        # For chasing, we want to move toward the target
        primary_direction = calculate_direction_to_target(current_pos, target_pos)

    # Check if primary direction is passable
    if is_direction_passable(current_pos, primary_direction, collision_map):
        return primary_direction

    # Try alternative directions
    alternatives = get_alternative_directions(primary_direction)
    for alt_direction in alternatives:
        if is_direction_passable(current_pos, alt_direction, collision_map):
            return alt_direction

    # If no direction is passable, return the primary direction anyway
    # (the movement function will handle the collision)
    return primary_direction


def _update_ai_behavior(
    monster: Monster,
    ai_behavior: AIBehavior,
    player_position: Optional[Position],
    dt: float,
    collision_map: List[List[bool]],
    base_move_speed: float = 64.0,
    attacked_monsters: Optional[List[str]] = None
) -> Position:
    """
    Update a monster's AI behavior and return new position.

    Args:
        monster: The monster entity
        ai_behavior: The AI behavior configuration and state
        player_position: Current player position (None if player not visible)
        dt: Delta time in seconds
        collision_map: 2D collision map for the level
        base_move_speed: Base movement speed in pixels per second
        attacked_monsters: List of monster IDs that were attacked this frame

    Returns:
        New position for the monster
    """
    current_pos = monster.position

    # Check if this monster was attacked
    if attacked_monsters and monster.id in attacked_monsters:
        ai_behavior.has_been_attacked = True
        if monster.ai_behavior in ["peaceful", "skittish"]:
            ai_behavior.transition_to_state(AIState.FLEEING, player_position)

    # Handle different AI states
    if ai_behavior.current_state == AIState.FLEEING:
        # Check if we should stop fleeing
        should_stop_fleeing = False

        if player_position:
            distance_to_player = calculate_distance(current_pos, player_position)
            # Stop fleeing if player is far enough away
            if distance_to_player >= ai_behavior.min_safe_distance:
                should_stop_fleeing = True
        elif ai_behavior.is_fleeing_expired():
            # Stop fleeing if duration expired and no player visible
            should_stop_fleeing = True

        if should_stop_fleeing:
            ai_behavior.transition_to_state(AIState.WANDERING)
            return current_pos

        # Continue fleeing from player or last known position
        threat_pos = player_position or ai_behavior.last_known_player_position
        if threat_pos:
            # Check if we're stuck and need to try a different direction
            is_stuck = ai_behavior.update_stuck_detection(current_pos)

            if is_stuck or ai_behavior.current_flee_direction is None:
                # Try a random escape direction to get unstuck or set initial direction
                flee_direction = get_random_escape_direction(current_pos, threat_pos, collision_map)
                ai_behavior.current_flee_direction = flee_direction
            else:
                # Continue with current flee direction if it's still valid
                if is_direction_passable(current_pos, ai_behavior.current_flee_direction, collision_map):
                    flee_direction = ai_behavior.current_flee_direction
                else:
                    # Current direction blocked, find a new one
                    flee_direction = find_best_direction(current_pos, threat_pos, collision_map, is_fleeing=True)
                    ai_behavior.current_flee_direction = flee_direction

            # Add some randomness to flee speed (80% to 120% of base flee speed)
            speed_randomness = random.uniform(0.8, 1.2)
            speed_multiplier = ai_behavior.flee_speed_multiplier * speed_randomness

            # Move away from threat
            new_pos = _move_in_direction(
                current_pos, flee_direction, dt, base_move_speed * speed_multiplier, collision_map
            )
            return new_pos

        return current_pos

    elif ai_behavior.current_state == AIState.CHASING:
        if not player_position:
            # Lost sight of player, return to wandering
            ai_behavior.transition_to_state(AIState.WANDERING)
            return current_pos

        # Check if close enough to attack
        distance_to_player = calculate_distance(current_pos, player_position)
        if distance_to_player <= ai_behavior.attack_range:
            ai_behavior.transition_to_state(AIState.ATTACKING, player_position)
            return current_pos

        # Chase the player using smart pathfinding
        chase_direction = find_best_direction(current_pos, player_position, collision_map, is_fleeing=False)
        speed_multiplier = ai_behavior.chase_speed_multiplier

        new_pos = _move_in_direction(
            current_pos, chase_direction, dt, base_move_speed * speed_multiplier, collision_map
        )
        ai_behavior.last_known_player_position = player_position
        return new_pos

    elif ai_behavior.current_state == AIState.ATTACKING:
        # Stay in position and attack (actual attack logic handled elsewhere)
        if not player_position:
            ai_behavior.transition_to_state(AIState.WANDERING)
            return current_pos

        distance_to_player = calculate_distance(current_pos, player_position)
        if distance_to_player > ai_behavior.attack_range:
            # Player moved away, chase again
            ai_behavior.transition_to_state(AIState.CHASING, player_position)

        return current_pos

    else:  # WANDERING or RETURNING
        # Check for player detection
        if player_position:
            distance_to_player = calculate_distance(current_pos, player_position)

            if monster.ai_behavior == "aggressive":
                # Aggressive monsters chase when player is detected
                if distance_to_player <= ai_behavior.detection_radius:
                    ai_behavior.transition_to_state(AIState.CHASING, player_position)
                    ai_behavior.last_known_player_position = player_position
                    return current_pos
            elif monster.ai_behavior == "skittish":
                # Skittish animals flee when player gets too close
                if distance_to_player <= ai_behavior.proximity_flee_radius:
                    ai_behavior.transition_to_state(AIState.FLEEING, player_position)
                    ai_behavior.last_known_player_position = player_position
                    return current_pos
            elif monster.ai_behavior == "peaceful":
                # Peaceful animals don't flee from proximity, only from attacks
                pass

        # Default to normal wandering behavior
        return current_pos


def _move_in_direction(
    current_pos: Position,
    direction: Direction,
    dt: float,
    move_speed: float,
    collision_map: List[List[bool]]
) -> Position:
    """Move an entity in a specific direction, handling collision detection."""

    # Calculate movement vector
    move_distance = move_speed * dt
    dx, dy = 0, 0

    # Handle all 8 directions including diagonals
    direction_map = {
        Direction.NORTH: (0, -move_distance),
        Direction.NORTHEAST: (move_distance * 0.707, -move_distance * 0.707),  # Normalize diagonal movement
        Direction.EAST: (move_distance, 0),
        Direction.SOUTHEAST: (move_distance * 0.707, move_distance * 0.707),
        Direction.SOUTH: (0, move_distance),
        Direction.SOUTHWEST: (-move_distance * 0.707, move_distance * 0.707),
        Direction.WEST: (-move_distance, 0),
        Direction.NORTHWEST: (-move_distance * 0.707, -move_distance * 0.707),
    }

    dx, dy = direction_map.get(direction, (0, 0))

    # Calculate new position
    new_x = current_pos.x + dx
    new_y = current_pos.y + dy
    new_pos = Position(new_x, new_y)

    # Convert pixel positions to tile positions for collision checking
    tile_size = 128  # Default tile size
    new_tile_x = int(new_pos.x // tile_size)
    new_tile_y = int(new_pos.y // tile_size)

    # Check bounds
    if (new_tile_y < 0 or new_tile_y >= len(collision_map) or
        new_tile_x < 0 or new_tile_x >= len(collision_map[0])):
        return current_pos

    # Check collision - if new tile is blocked, don't move
    if collision_map[new_tile_y][new_tile_x]:
        return current_pos

    return new_pos


def get_monsters_ready_to_attack(
    monsters: Dict[str, Monster],
    player_position: Optional[Position]
) -> List[str]:
    """
    Get list of monster IDs that are ready to attack the player.

    Args:
        monsters: Dictionary of monsters by ID
        player_position: Current player position

    Returns:
        List of monster IDs that should attack this frame
    """
    attacking_monsters = []

    if not player_position:
        return attacking_monsters

    current_time = time.time()

    for monster_id, monster in monsters.items():
        if (hasattr(monster, 'ai_behavior_instance') and
            monster.ai_behavior_instance and
            monster.ai_behavior_instance.current_state == AIState.ATTACKING):

            # Check if enough time has passed since last attack
            if current_time - monster.ai_behavior_instance.last_attack_time >= monster.ai_behavior_instance.attack_cooldown:
                # Check if player is still in range
                distance_to_player = calculate_distance(monster.position, player_position)
                if distance_to_player <= monster.ai_behavior_instance.attack_range:
                    attacking_monsters.append(monster_id)
                    # Update last attack time
                    monster.ai_behavior_instance.last_attack_time = current_time

    return attacking_monsters
